import '../../../models/app_user.dart';
import '../../../models/document_model.dart';
import '../../../utils/enums.dart';

abstract class ValidationContentRepository {
  Future<List<AppUser>> getPendingValidations();
  Future<void> approveUser(String userId);
  Future<void> rejectUser(String userId, String reason);
  Future<void> startValidation(String userId);
}

class ValidationContentRepositoryImpl implements ValidationContentRepository {
  @override
  Future<List<AppUser>> getPendingValidations() async {
    await Future.delayed(const Duration(milliseconds: 500));

    // Mock data for pending validations
    return [
      AppUser(
        id: '1',
        name: 'Dr. <PERSON>',
        email: '<EMAIL>',
        phone: '(11) 99999-9999',
        type: 'Profissional',
        specialty: 'Fisioterapia',
        location: 'São Paulo, SP',
        status: UserStatusEnum.pending,
        appointments: 0,
        lastAccess: DateTime.now().subtract(const Duration(days: 1)),
        hasResponses: false, // Novos Cadastros: sem nenhuma resposta
        documents: [
          DocumentModel(
            id: '1',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.pending,
          ),
          DocumentModel(
            id: '2',
            title: 'Certificado Profissional',
            url: '#',
            status: DocumentStatus.pending,
          ),
        ],
      ),
      AppUser(
        id: '2',
        name: 'Ana Beatriz Silva',
        email: '<EMAIL>',
        phone: '(11) 88888-8888',
        type: 'Paciente',
        specialty: null,
        location: 'Rio de Janeiro, RJ',
        status: UserStatusEnum.pending,
        appointments: 0,
        lastAccess: DateTime.now().subtract(const Duration(days: 2)),
        hasResponses: false, // Novos Cadastros: sem nenhuma resposta
        documents: [
          DocumentModel(
            id: '3',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.pending,
          ),
          DocumentModel(
            id: '4',
            title: 'Comprovante de Residência',
            url: '#',
            status: DocumentStatus.pending,
          ),
        ],
      ),
      AppUser(
        id: '3',
        name: 'Dr. Carlos Mendes',
        email: '<EMAIL>',
        phone: '(11) 77777-7777',
        type: 'Profissional',
        specialty: 'Psicologia',
        location: 'Belo Horizonte, MG',
        status: UserStatusEnum.inReview,
        appointments: 0,
        lastAccess: DateTime.now().subtract(const Duration(hours: 3)),
        hasResponses: true, // Alterações Pendentes: pelo menos 1 respondido
        documents: [
          DocumentModel(
            id: '5',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.pending,
          ),
          DocumentModel(
            id: '6',
            title: 'Certificado CRP',
            url: '#',
            status: DocumentStatus.pending,
          ),
        ],
      ),
      AppUser(
        id: '4',
        name: 'Maria Santos',
        email: '<EMAIL>',
        phone: '(11) 66666-6666',
        type: 'Paciente',
        specialty: null,
        location: 'Salvador, BA',
        status: UserStatusEnum.rejected,
        appointments: 0,
        lastAccess: DateTime.now().subtract(const Duration(days: 5)),
        hasResponses: true, // Cadastros Recusados: já foi avaliado
        documents: [
          DocumentModel(
            id: '7',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.rejected,
          ),
        ],
      ),
      AppUser(
        id: '5',
        name: 'Dr. João Silva',
        email: '<EMAIL>',
        phone: '(11) 55555-5555',
        type: 'Profissional',
        specialty: 'Enfermagem',
        location: 'Recife, PE',
        status: UserStatusEnum.pendingUpdate,
        appointments: 0,
        lastAccess: DateTime.now().subtract(const Duration(days: 3)),
        hasResponses: true, // Alterações Pendentes: pelo menos 1 respondido
        documents: [
          DocumentModel(
            id: '8',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.approved,
          ),
          DocumentModel(
            id: '9',
            title: 'Certificado COREN',
            url: '#',
            status: DocumentStatus.pending,
          ),
        ],
      ),
      AppUser(
        id: '6',
        name: 'Dra. Fernanda Costa',
        email: '<EMAIL>',
        phone: '(11) 44444-4444',
        type: 'Profissional',
        specialty: 'Nutrição',
        location: 'Fortaleza, CE',
        status: UserStatusEnum.active,
        appointments: 15,
        lastAccess: DateTime.now().subtract(const Duration(days: 1)),
        hasResponses: true, // Minhas Análises: usuário aprovado
        documents: [
          DocumentModel(
            id: '10',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.approved,
          ),
          DocumentModel(
            id: '11',
            title: 'Certificado CRN',
            url: '#',
            status: DocumentStatus.approved,
          ),
        ],
      ),
      AppUser(
        id: '7',
        name: 'Roberto Oliveira',
        email: '<EMAIL>',
        phone: '(11) 33333-3333',
        type: 'Paciente',
        specialty: null,
        location: 'Brasília, DF',
        status: UserStatusEnum.active,
        appointments: 8,
        lastAccess: DateTime.now().subtract(const Duration(hours: 12)),
        hasResponses: true, // Minhas Análises: usuário aprovado
        documents: [
          DocumentModel(
            id: '12',
            title: 'Documento de Identidade',
            url: '#',
            status: DocumentStatus.approved,
          ),
          DocumentModel(
            id: '13',
            title: 'Comprovante de Residência',
            url: '#',
            status: DocumentStatus.approved,
          ),
        ],
      ),
    ];
  }

  @override
  Future<void> approveUser(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // TODO: Implement API call
  }

  @override
  Future<void> rejectUser(String userId, String reason) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // TODO: Implement API call
  }

  @override
  Future<void> startValidation(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // TODO: Implement API call
  }
}
