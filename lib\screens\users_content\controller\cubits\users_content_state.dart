import '../../../../models/app_user.dart';

class UserContentState {
  final List<AppUser> all;
  final List<AppUser> filtered;
  final String filter;
  final String search;

  const UserContentState({
    this.all = const [],
    this.filtered = const [],
    this.filter = 'Todos',
    this.search = '',
  });

  UserContentState copyWith({
    List<AppUser>? all,
    List<AppUser>? filtered,
    String? filter,
    String? search,
  }) {
    return UserContentState(
      all: all ?? this.all,
      filtered: filtered ?? this.filtered,
      filter: filter ?? this.filter,
      search: search ?? this.search,
    );
  }
}
