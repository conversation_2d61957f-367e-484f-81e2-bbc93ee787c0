import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';

class FilterChipWidget extends StatelessWidget {
  final String label;
  final String value;
  final bool isSelected;
  final ValueChanged<String> onSelected;

  const FilterChipWidget({
    super.key,
    required this.label,
    required this.value,
    required this.isSelected,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: ChoiceChip(
          elevation: isSelected ? 1 : 0,
          label: Center(
            child: Text(
              label,
              style: TextStyle(
                color: isSelected ? const Color(0xFF27AE60) : Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          selected: isSelected,
          onSelected: (_) => onSelected(value),
          color: const WidgetStateProperty.fromMap({
            WidgetState.hovered: AppTheme.backgroundWhite,
            WidgetState.selected: AppTheme.backgroundWhite,
            WidgetState.focused: AppTheme.backgroundWhite,
            WidgetState.any: background,
          }),
          backgroundColor: background,
          selectedColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: const BorderSide(
              color: Colors.transparent,
              width: 0,
            ),
          ),
          pressElevation: 0,
          showCheckmark: false,
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
        ),
      ),
    );
  }
}
