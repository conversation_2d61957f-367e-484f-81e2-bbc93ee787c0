import '../../../../models/admin.dart';

class AdministratorsContentState {
  final List<Admin> all;
  final String filter;
  final String search;

  AdministratorsContentState({
    required this.all,
    required this.filter,
    required this.search,
  });

  factory AdministratorsContentState.initial() {
    return AdministratorsContentState(
      all: [],
      filter: 'all',
      search: '',
    );
  }

  AdministratorsContentState copyWith({
    List<Admin>? all,
    List<Admin>? filtered,
    String? filter,
    String? search,
  }) {
    return AdministratorsContentState(
      all: all ?? this.all,
      filter: filter ?? this.filter,
      search: search ?? this.search,
    );
  }
}
