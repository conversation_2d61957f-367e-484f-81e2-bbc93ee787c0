import '../../../../models/admin.dart';

class AdministratorsContentState {
  final List<Admin> all;
  final List<Admin> filtered;
  final String filter;
  final String search;
  final bool isLoading;
  final String? errorMessage;

  AdministratorsContentState({
    required this.all,
    required this.filtered,
    required this.filter,
    required this.search,
    this.isLoading = false,
    this.errorMessage,
  });

  factory AdministratorsContentState.initial() {
    return AdministratorsContentState(
      all: [],
      filtered: [],
      filter: 'all',
      search: '',
      isLoading: false,
    );
  }

  AdministratorsContentState copyWith({
    List<Admin>? all,
    List<Admin>? filtered,
    String? filter,
    String? search,
    bool? isLoading,
    String? errorMessage,
  }) {
    return AdministratorsContentState(
      all: all ?? this.all,
      filtered: filtered ?? this.filtered,
      filter: filter ?? this.filter,
      search: search ?? this.search,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }
}
