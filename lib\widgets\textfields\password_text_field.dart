import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/validators.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

class PasswordTextfield extends TextFieldCustom {
  const PasswordTextfield({
    super.key,
    super.onChanged,
    super.onSubmit,
    super.errorMessage,
    super.isEnabled,
    super.controller,
    super.initialValue,
    super.textInputAction,
    super.hintText,
    super.label = 'Senha',
  }) : super(
          keyboardType: TextInputType.text,
          isPassword: true,
          validator: validateDefault,
        );
}
