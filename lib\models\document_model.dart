import 'package:homecare_nexus_clone/utils/enums.dart';

class DocumentModel {
  final String id;
  final String title;
  final String url;
  final DocumentStatus? status;

  DocumentModel({
    required this.id,
    required this.title,
    required this.url,
    this.status,
  });

  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    String? raw = json['status'];
    DocumentStatus? ds;
    if (raw != null) {
      final r = raw.toString().toLowerCase();
      ds = r == 'aprovado'
          ? DocumentStatus.approved
          : r == 'rejeitado'
              ? DocumentStatus.rejected
              : DocumentStatus.pending;
    }
    return DocumentModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      url: json['url'] ?? '',
      status: ds,
    );
  }
}
