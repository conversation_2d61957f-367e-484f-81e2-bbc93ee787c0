import 'package:equatable/equatable.dart';
import '../../../../models/app_user.dart';

class ValidationContentState extends Equatable {
  final List<AppUser> all;
  final List<AppUser> filtered;
  final String selectedFilter;
  final bool isLoading;
  final String? error;

  const ValidationContentState({
    this.all = const [],
    this.filtered = const [],
    this.selectedFilter = 'Novos Cadastros',
    this.isLoading = false,
    this.error,
  });

  ValidationContentState copyWith({
    List<AppUser>? all,
    List<AppUser>? filtered,
    String? selectedFilter,
    bool? isLoading,
    String? error,
  }) {
    return ValidationContentState(
      all: all ?? this.all,
      filtered: filtered ?? this.filtered,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [all, filtered, selectedFilter, isLoading, error];
}