import 'package:flutter/services.dart';
import 'package:homecare_nexus_clone/utils/formatters.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

class NameTextField extends TextFieldCustom {
  NameTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.controller,
    super.hintText = 'Nome Sobrenome',
    super.isEnabled,
    super.textInputAction,
    super.validator,
    super.label,
  }) : super(
          keyboardType: TextInputType.text,
          inputFormatters: [Formatters.nameFormatter],
        );
}
