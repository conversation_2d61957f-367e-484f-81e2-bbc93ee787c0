import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Serviço de autenticação que integra Firebase Auth com Provider
class FirebaseAuthService extends ChangeNotifier {
  final FirebaseAuth _firebaseAuth;
  User? _currentUser;
  bool _isInitialized = false;

  FirebaseAuthService({FirebaseAuth? firebaseAuth})
      : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance {
    _init();
  }

  /// Usuário atual
  User? get currentUser => _currentUser;

  /// Verifica se está autenticado
  bool get isAuthenticated => _currentUser != null;

  /// Verifica se foi inicializado
  bool get isInitialized => _isInitialized;

  /// Email do usuário atual
  String? get userEmail => _currentUser?.email;

  /// Nome do usuário atual
  String? get userName => _currentUser?.displayName;

  /// UID do usuário atual
  String? get userId => _currentUser?.uid;

  /// Inicializa o serviço e monitora mudanças de autenticação
  Future<void> _init() async {
    // Monitora mudanças no estado de autenticação
    _firebaseAuth.authStateChanges().listen((User? user) async {
      _currentUser = user;
      
      if (user != null) {
        await _saveUserDataLocally(user);
      } else {
        await _clearUserDataLocally();
      }
      
      if (!_isInitialized) {
        _isInitialized = true;
      }
      
      notifyListeners();
    });

    // Carrega dados salvos localmente
    await _loadUserDataLocally();
  }

  /// Realiza login com email e senha
  Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );
      
      if (result.user != null) {
        return AuthResult.success(result.user!);
      } else {
        return AuthResult.failure('Falha na autenticação');
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_handleFirebaseAuthException(e));
    } catch (e) {
      return AuthResult.failure('Erro inesperado: $e');
    }
  }

  /// Realiza logout
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      // O listener authStateChanges já vai limpar os dados
    } catch (e) {
      throw Exception('Erro ao fazer logout: $e');
    }
  }

  /// Envia email de redefinição de senha
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email.trim());
    } on FirebaseAuthException catch (e) {
      throw Exception(_handleFirebaseAuthException(e));
    } catch (e) {
      throw Exception('Erro ao enviar email de redefinição: $e');
    }
  }

  /// Salva dados do usuário localmente
  Future<void> _saveUserDataLocally(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isLoggedIn', true);
      await prefs.setString('userId', user.uid);
      await prefs.setString('userEmail', user.email ?? '');
      await prefs.setString('userName', user.displayName ?? '');
    } catch (e) {
      debugPrint('Erro ao salvar dados localmente: $e');
    }
  }

  /// Limpa dados do usuário localmente
  Future<void> _clearUserDataLocally() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('isLoggedIn');
      await prefs.remove('userId');
      await prefs.remove('userEmail');
      await prefs.remove('userName');
    } catch (e) {
      debugPrint('Erro ao limpar dados localmente: $e');
    }
  }

  /// Carrega dados do usuário salvos localmente
  Future<void> _loadUserDataLocally() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
      
      if (isLoggedIn && _currentUser == null) {
        // Se há dados salvos mas não há usuário atual, 
        // aguarda o Firebase verificar o estado
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      debugPrint('Erro ao carregar dados localmente: $e');
    }
  }

  /// Trata exceções específicas do Firebase Auth
  String _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'Usuário não encontrado. Verifique o email informado.';
      case 'wrong-password':
        return 'Senha incorreta. Tente novamente.';
      case 'invalid-email':
        return 'Email inválido. Verifique o formato do email.';
      case 'user-disabled':
        return 'Esta conta foi desabilitada. Entre em contato com o suporte.';
      case 'too-many-requests':
        return 'Muitas tentativas de login. Tente novamente mais tarde.';
      case 'operation-not-allowed':
        return 'Operação não permitida. Entre em contato com o suporte.';
      case 'weak-password':
        return 'A senha é muito fraca. Use uma senha mais forte.';
      case 'email-already-in-use':
        return 'Este email já está sendo usado por outra conta.';
      case 'invalid-credential':
        return 'Credenciais inválidas. Verifique email e senha.';
      case 'network-request-failed':
        return 'Erro de conexão. Verifique sua internet e tente novamente.';
      default:
        return 'Erro de autenticação: ${e.message ?? 'Erro desconhecido'}';
    }
  }
}

/// Classe para representar o resultado da autenticação
class AuthResult {
  final bool isSuccess;
  final User? user;
  final String? errorMessage;

  AuthResult._({
    required this.isSuccess,
    this.user,
    this.errorMessage,
  });

  factory AuthResult.success(User user) {
    return AuthResult._(
      isSuccess: true,
      user: user,
    );
  }

  factory AuthResult.failure(String errorMessage) {
    return AuthResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }
}
