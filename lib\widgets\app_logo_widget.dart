import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';

enum AppLogoSize { small, large }

class AppLogoWidget extends StatelessWidget {
  final AppLogoSize size;

  const AppLogoWidget({super.key, this.size = AppLogoSize.small});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: size == AppLogoSize.small
          ? MainAxisAlignment.start
          : MainAxisAlignment.center,
      children: [
        Container(
          width: size == AppLogoSize.small ? 31 : 42,
          height: size == AppLogoSize.small ? 31 : 42,
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.favorite_border,
            color: Colors.white,
            size: size == AppLogoSize.small ? 16 : 24,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Achei <PERSON>de',
              style: size == AppLogoSize.small
                  ? textSm.copyWith(
                      color: sideBarForeground,
                      fontWeight: FontWeight.w700,
                    )
                  : text2xl.copyWith(fontWeight: FontWeight.w700, height: 1),
            ),
            const SizedBox(height: 4),
            Text(
              size == AppLogoSize.small
                  ? 'Admin Panel'
                  : 'Painel Administrativo',
              style: size == AppLogoSize.small
                  ? textXs.copyWith(color: sideBarForeground)
                  : textSm,
            ),
          ],
        ),
      ],
    );
  }
}
