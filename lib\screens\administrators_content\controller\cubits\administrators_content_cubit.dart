import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../models/admin.dart';
import '../../../../utils/enums.dart';
import '../administrators_content_service.dart';
import 'administrators_content_state.dart';

class AdministratorsContentCubit extends Cubit<AdministratorsContentState> {
  final AdministratorsContentService _service;

  AdministratorsContentCubit(this._service)
      : super(AdministratorsContentState.initial());

  Future<void> loadAdmins() async {
    try {
      emit(state.copyWith(isLoading: true));
      final admins = await _service.fetchAdmins();
      emit(state.copyWith(
        all: admins,
        filtered: admins,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Erro ao carregar administradores: $e',
      ));
    }
  }

  void setStatus(String adminId, UserStatusEnum status) {
    final updated = state.all.map((a) {
      if (a.id == adminId) {
        return Admin(
          id: a.id,
          name: a.name,
          email: a.email,
          phone: a.phone,
          type: a.type,
          specialty: a.specialty,
          location: a.location,
          status: status,
          appointments: a.appointments,
          lastAccess: a.lastAccess,
          documents: a.documents,
          username: a.username,
          role: a.role,
          createdAt: a.createdAt,
        );
      }
      return a;
    }).toList();
    // reaplica filtro atual
    emit(state.copyWith(all: updated));
  }

  /// Cria um novo administrador no Firebase
  Future<bool> createAdmin({
    required String name,
    required String username,
    required String email,
    required String password,
    required AdmEnum role,
    String? phone,
  }) async {
    try {
      emit(state.copyWith(isLoading: true));

      final result = await _service.createAdmin(
        name: name,
        username: username,
        email: email,
        password: password,
        role: role,
        phone: phone,
      );

      if (result.isSuccess && result.admin != null) {
        final updated = [result.admin!, ...state.all];
        final filteredUpdated = [result.admin!, ...state.filtered];
        emit(state.copyWith(
          all: updated,
          filtered: filteredUpdated,
          isLoading: false,
          errorMessage: null,
        ));
        return true;
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: result.errorMessage ?? 'Erro ao criar administrador',
        ));
        return false;
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Erro inesperado: $e',
      ));
      return false;
    }
  }

  /// Adiciona um novo administrador e atualiza o estado (método legado)
  void addAdmin(Admin admin) {
    final updated = [admin, ...state.all];
    final filteredUpdated = [admin, ...state.filtered];
    emit(state.copyWith(all: updated, filtered: filteredUpdated));
  }

  /// Atualiza um administrador existente (por id)
  Future<void> updateAdmin(Admin admin) async {
    try {
      await _service.updateAdmin(admin);
      final updated =
          state.all.map((a) => a.id == admin.id ? admin : a).toList();
      final filteredUpdated =
          state.filtered.map((a) => a.id == admin.id ? admin : a).toList();
      emit(state.copyWith(all: updated, filtered: filteredUpdated));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Erro ao atualizar administrador: $e'));
    }
  }

  /// Limpa mensagens de erro
  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }
}
