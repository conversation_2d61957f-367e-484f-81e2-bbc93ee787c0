import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../models/admin.dart';
import '../../../../utils/enums.dart';
import '../administrators_content_service.dart';
import 'administrators_content_state.dart';

class AdministratorsContentCubit extends Cubit<AdministratorsContentState> {
  final AdministratorsContentService _service;

  AdministratorsContentCubit(this._service)
      : super(AdministratorsContentState.initial());

  Future<void> loadAdmins() async {
    final admins = await _service.fetchAdmins();
    emit(state.copyWith(all: admins, filtered: admins));
  }

  void setStatus(String adminId, UserStatusEnum status) {
    final updated = state.all.map((a) {
      if (a.id == adminId) {
        return Admin(
          id: a.id,
          name: a.name,
          email: a.email,
          phone: a.phone,
          type: a.type,
          specialty: a.specialty,
          location: a.location,
          status: status,
          appointments: a.appointments,
          lastAccess: a.lastAccess,
          documents: a.documents,
          username: a.username,
          role: a.role,
          createdAt: a.createdAt,
        );
      }
      return a;
    }).toList();
    // reaplica filtro atual
    emit(state.copyWith(all: updated));
  }

  /// Adiciona um novo administrador e atualiza o estado
  void addAdmin(Admin admin) {
    final updated = [admin, ...state.all];
    emit(state.copyWith(all: updated));
  }

  /// Atualiza um administrador existente (por id)
  void updateAdmin(Admin admin) {
    final updated = state.all.map((a) => a.id == admin.id ? admin : a).toList();
    emit(state.copyWith(all: updated));
  }
}
