import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/widgets/containers/container_bordered.dart';

class ContentContainer extends ContainerBordered {
  final String title;
  final String subtitle;
  final List<Widget> children;

  ContentContainer({
    super.key,
    required this.title,
    required this.subtitle,
    required this.children,
  }) : super(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lista de Usuários',
                        style: text2xl.copyWith(
                          fontWeight: FontWeight.w600,
                          height: 1,
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Visualize e gerencie todos os usuários cadastrados na plataforma',
                        style: textSm,
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 24),
              ...children,
            ],
          ),
        );
}
