import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/widgets/containers/container_bordered.dart';

import '../utils/app_theme.dart';

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final Color? valueColor;
  final String? change;
  final ChangeType? changeType;
  final String? subtitle;
  final String? description;
  final IconData? icon;
  final Color? iconColor;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    this.valueColor = foreground,
    this.change,
    this.changeType,
    this.subtitle,
    this.description,
    this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return ContainerBordered(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title, style: textSm),
              if (icon != null && iconColor != null)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor!.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 20,
                    color: iconColor,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          Text(value, style: text2xl.copyWith(color: valueColor)),
          if (changeType != null && change != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getChangeColor()!.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getChangeIcon(),
                        size: 12,
                        color: _getChangeColor(),
                      ),
                      Text(
                        change!,
                        style: textXs.copyWith(color: _getChangeColor()),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                if (subtitle != null && subtitle!.isNotEmpty)
                  Text(subtitle!, style: textXs),
              ],
            ),
          ],
          if (description != null && description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(description!, style: textXs),
          ],
        ],
      ),
    );
  }

  Color? _getChangeColor() {
    if (changeType == null) return null;
    switch (changeType!) {
      case ChangeType.positive:
        return AppTheme.primaryGreen;
      case ChangeType.negative:
        return AppTheme.errorRed;
      case ChangeType.neutral:
        return AppTheme.textGray;
    }
  }

  IconData? _getChangeIcon() {
    if (changeType == null) return null;
    switch (changeType!) {
      case ChangeType.positive:
        return Icons.trending_up;
      case ChangeType.negative:
        return Icons.trending_down;
      case ChangeType.neutral:
        return Icons.trending_flat;
    }
  }
}

enum ChangeType { positive, negative, neutral }
