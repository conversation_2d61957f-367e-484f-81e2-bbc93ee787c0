import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class LoginRepository {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  LoginRepository({
    FirebaseAuth? firebaseAuth,
    FirebaseFirestore? firestore,
  })  : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance;

  /// Busca email pelo username na collection de administradores
  Future<String?> getEmailByUsername(String username) async {
    try {
      // Busca primeiro na collection de administradores
      final QuerySnapshot adminSnapshot = await _firestore
          .collection('administrators')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();

      if (adminSnapshot.docs.isNotEmpty) {
        final data = adminSnapshot.docs.first.data() as Map<String, dynamic>;
        return data['email'] as String?;
      }

      // Se não encontrar, busca na collection de usuários
      final QuerySnapshot userSnapshot = await _firestore
          .collection('users')
          .where('username', isEqualTo: username)
          .where('userType', isEqualTo: 'administrator')
          .limit(1)
          .get();

      if (userSnapshot.docs.isNotEmpty) {
        final data = userSnapshot.docs.first.data() as Map<String, dynamic>;
        return data['email'] as String?;
      }

      return null;
    } catch (e) {
      throw Exception('Erro ao buscar usuário: $e');
    }
  }

  /// Realiza login com usuário e senha
  Future<User?> signInWithUsernameAndPassword({
    required String username,
    required String password,
  }) async {
    try {
      // Primeiro, busca o email pelo username
      final email = await getEmailByUsername(username);

      if (email == null) {
        throw Exception('Usuário não encontrado. Verifique o nome de usuário.');
      }

      // Depois faz login com email e senha
      return await signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('Erro inesperado durante o login: $e');
    }
  }

  /// Realiza login com email e senha usando Firebase Auth
  Future<User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result =
          await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return result.user;
    } on FirebaseAuthException catch (e) {
      throw _handleFirebaseAuthException(e);
    } catch (e) {
      throw Exception('Erro inesperado durante o login: $e');
    }
  }

  /// Realiza logout
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      throw Exception('Erro ao fazer logout: $e');
    }
  }

  /// Verifica se há um usuário logado
  User? getCurrentUser() {
    return _firebaseAuth.currentUser;
  }

  /// Stream para monitorar mudanças no estado de autenticação
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  /// Envia email de redefinição de senha
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleFirebaseAuthException(e);
    } catch (e) {
      throw Exception('Erro ao enviar email de redefinição: $e');
    }
  }

  /// Trata exceções específicas do Firebase Auth
  String _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'Usuário não encontrado. Verifique o email informado.';
      case 'wrong-password':
        return 'Senha incorreta. Tente novamente.';
      case 'invalid-email':
        return 'Email inválido. Verifique o formato do email.';
      case 'user-disabled':
        return 'Esta conta foi desabilitada. Entre em contato com o suporte.';
      case 'too-many-requests':
        return 'Muitas tentativas de login. Tente novamente mais tarde.';
      case 'operation-not-allowed':
        return 'Operação não permitida. Entre em contato com o suporte.';
      case 'weak-password':
        return 'A senha é muito fraca. Use uma senha mais forte.';
      case 'email-already-in-use':
        return 'Este email já está sendo usado por outra conta.';
      case 'invalid-credential':
        return 'Credenciais inválidas. Verifique email e senha.';
      case 'network-request-failed':
        return 'Erro de conexão. Verifique sua internet e tente novamente.';
      default:
        return 'Erro de autenticação: ${e.message ?? 'Erro desconhecido'}';
    }
  }
}
