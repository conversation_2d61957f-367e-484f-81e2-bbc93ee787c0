import 'package:get_it/get_it.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/administrators_content_repository.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/administrators_content_service.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/cubits/administrators_content_cubit.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/firebase_admin_service.dart';
import 'package:homecare_nexus_clone/screens/login/controller/cubits/login_cubit.dart';
import 'package:homecare_nexus_clone/screens/login/controller/login_repository.dart';
import 'package:homecare_nexus_clone/screens/login/controller/login_service.dart';
import 'package:homecare_nexus_clone/screens/validation_content/controller/cubits/validation_content_cubit.dart';
import 'package:homecare_nexus_clone/screens/validation_content/controller/validation_content_repository.dart';
import 'package:homecare_nexus_clone/screens/validation_content/controller/validation_content_service.dart';

import '../screens/users_content/controller/cubits/users_content_cubit.dart';
import '../screens/users_content/controller/users_content_repository.dart';
import '../screens/users_content/controller/users_content_service.dart';

final getIt = GetIt.instance;

void setupDependencies() {
  // Login
  getIt.registerLazySingleton<LoginRepository>(() => LoginRepository());
  getIt.registerLazySingleton<LoginService>(
    () => LoginService(repository: getIt<LoginRepository>()),
  );
  getIt.registerLazySingleton<LoginCubit>(
    () => LoginCubit(loginService: getIt<LoginService>()),
  );

  // Validation Content
  getIt.registerLazySingleton<ValidationContentRepository>(
    () => ValidationContentRepositoryImpl(),
  );
  getIt.registerLazySingleton<ValidationContentService>(
    () => ValidationContentService(getIt<ValidationContentRepository>()),
  );
  getIt.registerLazySingleton<ValidationContentCubit>(
    () => ValidationContentCubit(getIt<ValidationContentService>()),
  );

  // Repositories
  getIt.registerLazySingleton<UserRepository>(() => UserRepository());
  getIt.registerLazySingleton<AdministratorsContentRepository>(
      () => AdministratorsContentRepository());

  // Firebase Services
  getIt.registerLazySingleton<FirebaseAdminService>(
      () => FirebaseAdminService());

  // Services
  getIt.registerLazySingleton<UserContentService>(
      () => UserContentService(getIt()));
  getIt.registerLazySingleton<AdministratorsContentService>(
      () => AdministratorsContentService(getIt(), firebaseService: getIt()));

  // Cubits
  getIt.registerSingleton<UserContentCubit>(UserContentCubit(getIt()));
  getIt.registerSingleton<AdministratorsContentCubit>(
      AdministratorsContentCubit(getIt()));
}
