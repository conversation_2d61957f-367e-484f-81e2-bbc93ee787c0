import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/core/global_instances.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/utils/extensions.dart';
import 'package:homecare_nexus_clone/widgets/buttons/primary_button.dart';
import 'package:homecare_nexus_clone/widgets/textfields/email_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/name_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/password_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

import '../../../../models/admin.dart';

class AdminCreateDialog extends StatefulWidget {
  final Admin? initialAdmin;
  final bool isCreateMode;

  const AdminCreateDialog(
      {super.key, this.initialAdmin, this.isCreateMode = true});

  @override
  State<AdminCreateDialog> createState() => _AdminCreateDialogState();
}

class _AdminCreateDialogState extends State<AdminCreateDialog> {
  late final TextEditingController _nameController;
  late final TextEditingController _usernameController;
  late final TextEditingController _emailController;
  late AdmEnum _selectedRole;
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialAdmin?.name);
    _usernameController =
        TextEditingController(text: widget.initialAdmin?.username);
    _emailController = TextEditingController(text: widget.initialAdmin?.email);
    _selectedRole = widget.initialAdmin?.role ?? AdmEnum.admin;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 450),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.initialAdmin == null
                            ? 'Criar Novo Administrador'
                            : 'Editar Administrador',
                        style: textLg,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.initialAdmin == null
                            ? 'Adicione um novo administrador à plataforma'
                            : 'Altere as informações do administrador selecionado',
                        style: textSm.copyWith(fontWeight: FontWeight.w400),
                      ),
                    ],
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: NameTextField(
                      label: 'Nome Completo',
                      hintText: 'Ex: João Silva',
                      controller: _nameController,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFieldCustom(
                      label: 'Nome de Usuário',
                      hintText: 'Ex: joao.silva',
                      controller: _usernameController,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              EmailTextField(
                hintText: 'Ex: <EMAIL>',
                controller: _emailController,
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Função',
                      style: textSm.copyWith(fontWeight: FontWeight.w600)),
                  const SizedBox(height: 4),
                  DropdownButtonFormField<AdmEnum>(
                    value: _selectedRole,
                    items: AdmEnum.values
                        .map((e) => DropdownMenuItem(
                              value: e,
                              child: Text(e.displayName),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedRole = value);
                      }
                    },
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8)),
                      focusedBorder: OutlineInputBorder(
                        borderSide:
                            const BorderSide(color: AppTheme.primaryGreen),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
              if (widget.isCreateMode) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: PasswordTextfield(
                        hintText: 'Senha temporária',
                        controller: _passwordController,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: PasswordTextfield(
                        label: 'Confirmar Senha',
                        hintText: 'Confirme a senha',
                        controller: _confirmPasswordController,
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomButton(
                    onPressed: () => Navigator.of(context).pop(),
                    label: 'Cancelar',
                    type: ButtonType.secondary,
                  ),
                  const SizedBox(width: 16),
                  CustomButton(
                    onPressed: () {
                      // validações simples
                      final name = _nameController.text.trim();
                      final username = _usernameController.text.trim();
                      final email = _emailController.text.trim();

                      if (name.isEmpty || username.isEmpty || email.isEmpty) {
                        // poderia mostrar um erro, por agora só retorna
                        return;
                      }

                      final admin = Admin(
                        id: widget.initialAdmin?.id ?? UniqueKey().toString(),
                        name: name,
                        email: email,
                        phone: widget.initialAdmin?.phone ?? '',
                        type: 'Admin',
                        specialty: null,
                        location: 'Sede',
                        status: widget.initialAdmin?.status ??
                            UserStatusEnum.active,
                        appointments: widget.initialAdmin?.appointments ?? 0,
                        lastAccess:
                            widget.initialAdmin?.lastAccess ?? DateTime.now(),
                        documents: widget.initialAdmin?.documents ?? [],
                        username: username,
                        role: _selectedRole,
                        createdAt:
                            widget.initialAdmin?.createdAt ?? DateTime.now(),
                      );

                      if (widget.isCreateMode) {
                        administratorsContentCubit.addAdmin(admin);
                      } else {
                        administratorsContentCubit.updateAdmin(admin);
                      }

                      Navigator.of(context).pop();
                    },
                    icon: Icon(widget.initialAdmin == null
                        ? Icons.person_add_alt_1
                        : Icons.edit_square),
                    label: widget.initialAdmin == null
                        ? 'Criar Administrador'
                        : 'Salvar Alterações',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
