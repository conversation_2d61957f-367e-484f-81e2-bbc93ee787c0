import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/core/global_instances.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/utils/extensions.dart';
import 'package:homecare_nexus_clone/widgets/buttons/primary_button.dart';
import 'package:homecare_nexus_clone/widgets/textfields/email_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/name_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/password_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

import '../../../../models/admin.dart';

class AdminCreateDialog extends StatefulWidget {
  final Admin? initialAdmin;
  final bool isCreateMode;

  const AdminCreateDialog(
      {super.key, this.initialAdmin, this.isCreateMode = true});

  @override
  State<AdminCreateDialog> createState() => _AdminCreateDialogState();
}

class _AdminCreateDialogState extends State<AdminCreateDialog> {
  late final TextEditingController _nameController;
  late final TextEditingController _usernameController;
  late final TextEditingController _emailController;
  late AdmEnum _selectedRole;
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialAdmin?.name);
    _usernameController =
        TextEditingController(text: widget.initialAdmin?.username);
    _emailController = TextEditingController(text: widget.initialAdmin?.email);
    _selectedRole = widget.initialAdmin?.role ?? AdmEnum.admin;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    final name = _nameController.text.trim();
    final username = _usernameController.text.trim();
    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();

    // Validações específicas
    if (widget.isCreateMode) {
      if (password.isEmpty) {
        _showError('Senha é obrigatória');
        return;
      }
      if (password != confirmPassword) {
        _showError('Senhas não coincidem');
        return;
      }
      if (password.length < 6) {
        _showError('Senha deve ter pelo menos 6 caracteres');
        return;
      }
    }

    setState(() => _isLoading = true);

    try {
      if (widget.isCreateMode) {
        // Criar novo administrador
        final success = await administratorsContentCubit.createAdmin(
          name: name,
          username: username,
          email: email,
          password: password,
          role: _selectedRole,
        );

        if (success) {
          if (mounted) {
            Navigator.of(context).pop();
            _showSuccess('Administrador criado com sucesso!');
          }
        } else {
          // Erro será mostrado pelo listener do BLoC
        }
      } else {
        // Atualizar administrador existente
        final admin = Admin(
          id: widget.initialAdmin!.id,
          name: name,
          email: email,
          phone: widget.initialAdmin?.phone ?? '',
          type: 'Admin',
          specialty: null,
          location: 'Sede',
          status: widget.initialAdmin!.status,
          appointments: widget.initialAdmin!.appointments,
          lastAccess: widget.initialAdmin!.lastAccess,
          documents: widget.initialAdmin!.documents,
          username: username,
          role: _selectedRole,
          createdAt: widget.initialAdmin!.createdAt,
        );

        await administratorsContentCubit.updateAdmin(admin);
        if (mounted) {
          Navigator.of(context).pop();
          _showSuccess('Administrador atualizado com sucesso!');
        }
      }
    } catch (e) {
      _showError('Erro inesperado: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.errorRed,
        ),
      );
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.primaryGreen,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 450),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.initialAdmin == null
                              ? 'Criar Novo Administrador'
                              : 'Editar Administrador',
                          style: textLg,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.initialAdmin == null
                              ? 'Adicione um novo administrador à plataforma'
                              : 'Altere as informações do administrador selecionado',
                          style: textSm.copyWith(fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: NameTextField(
                        label: 'Nome Completo',
                        hintText: 'Ex: João Silva',
                        controller: _nameController,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFieldCustom(
                        label: 'Nome de Usuário',
                        hintText: 'Ex: joao.silva',
                        controller: _usernameController,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                EmailTextField(
                  hintText: 'Ex: <EMAIL>',
                  controller: _emailController,
                ),
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Função',
                        style: textSm.copyWith(fontWeight: FontWeight.w600)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<AdmEnum>(
                      value: _selectedRole,
                      items: AdmEnum.values
                          .map((e) => DropdownMenuItem(
                                value: e,
                                child: Text(e.displayName),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => _selectedRole = value);
                        }
                      },
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8)),
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: AppTheme.primaryGreen),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
                if (widget.isCreateMode) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: PasswordTextfield(
                          hintText: 'Senha temporária',
                          controller: _passwordController,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: PasswordTextfield(
                          label: 'Confirmar Senha',
                          hintText: 'Confirme a senha',
                          controller: _confirmPasswordController,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 32),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomButton(
                      onPressed: () => Navigator.of(context).pop(),
                      label: 'Cancelar',
                      type: ButtonType.secondary,
                    ),
                    const SizedBox(width: 16),
                    CustomButton(
                      onPressed: _isLoading ? null : () => _handleSubmit(),
                      icon: Icon(widget.initialAdmin == null
                          ? Icons.person_add_alt_1
                          : Icons.edit_square),
                      label: widget.initialAdmin == null
                          ? 'Criar Administrador'
                          : 'Salvar Alterações',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
