import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/validators.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

class EmailTextField extends TextFieldCustom {
  const EmailTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.isEnabled,
    super.controller,
    super.initialValue,
    super.textInputAction,
    super.hintText,
  }) : super(
          label: 'Email',
          keyboardType: TextInputType.emailAddress,
          validator: validateEmail,
        );
}
