import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/view/widgets/admin_create_dialog.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';
import 'package:homecare_nexus_clone/utils/date_formatter.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/utils/extensions.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

import '../../../../models/admin.dart';

class AdminRow extends StatelessWidget {
  final Admin admin;
  final Color roleColor;
  final Color statusColor;
  final Color avatarColor;

  const AdminRow(
      {super.key,
      required this.admin,
      required this.roleColor,
      required this.statusColor,
      required this.avatarColor});

  TagType get _adminTag => switch (admin.role) {
        AdmEnum.superAdmin => TagType.primary,
        AdmEnum.admin => TagType.secondary,
        AdmEnum.moderator => TagType.border,
      };

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderGray),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  admin.name,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  '@${admin.username}',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textGray.withValues(alpha: 0.7),
                  ),
                ),
                Text(
                  admin.email,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textGray.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Align(
                alignment: Alignment.centerLeft,
                child:
                    TagWidget(title: admin.role.displayName, type: _adminTag)),
          ),
          Expanded(
            flex: 1,
            child: Align(
                alignment: Alignment.centerLeft,
                child: TagWidget(
                    title: admin.status.displayName,
                    type: admin.status.tagType)),
          ),
          Expanded(
            flex: 2,
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 14),
                const SizedBox(width: 4),
                Text(
                  DateFormatter.formatDate(admin.createdAt),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Row(
              children: [
                const Icon(Icons.access_time, size: 14),
                const SizedBox(width: 4),
                Text(
                  DateFormatter.formatDate(admin.lastAccess),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerLeft,
              child: PopupMenuButton<String>(
                padding: EdgeInsets.zero,
                icon: const Icon(Icons.more_vert, size: 18),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                onSelected: (value) {
                  if (value == 'edit') {
                    showDialog(
                      context: context,
                      builder: (context) => AdminCreateDialog(
                        initialAdmin: admin,
                        isCreateMode: false,
                      ),
                    );
                  } else if (value == 'password') {
                    // abrir modal de alteração de senha
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem<String>(
                    enabled: false,
                    child: Text(
                      'Ações',
                      style: textSm.copyWith(
                        fontWeight: FontWeight.w600,
                        color: foreground,
                      ),
                    ),
                  ),
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        const Icon(
                          Icons.edit_square,
                          size: 16,
                          color: foreground,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Editar',
                          style: textMd.copyWith(
                            color: foreground,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'password',
                    child: Row(
                      children: [
                        const Icon(
                          Icons.vpn_key_outlined,
                          size: 16,
                          color: foreground,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Alterar Senha',
                          style: textMd.copyWith(
                            color: foreground,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
