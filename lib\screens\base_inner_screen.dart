import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/view/widgets/admin_create_dialog.dart';
import 'package:homecare_nexus_clone/screens/login/view/widgets/logout_button.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/widgets/buttons/primary_button.dart';
import 'package:provider/provider.dart';

import '../services/navigation_service.dart';
import '../utils/app_theme.dart';
import '../widgets/sidebar.dart';
import 'administrators_content/view/administrators_content.dart';
import 'users_content/view/users_content.dart';
import 'validation_content/view/validation_content.dart';

class BaseInnerScreen extends StatefulWidget {
  const BaseInnerScreen({super.key});

  @override
  State<BaseInnerScreen> createState() => _BaseInnerScreenState();
}

class _BaseInnerScreenState extends State<BaseInnerScreen> {
  bool _isSidebarOpen = true;

  Widget _buildContent(String currentRoute) {
    switch (currentRoute) {
      case 'dashboard':
        return const Placeholder();
      case 'users':
        return const UsersContent();
      case 'administrators':
        return const AdministratorsContent();
      case 'validation':
        return const ValidationContent();
      case 'reports':
        return const Placeholder();
      case 'rankings':
        return const Placeholder();
      default:
        return const Placeholder();
    }
  }

  String _getPageTitle(String currentRoute) {
    switch (currentRoute) {
      case 'dashboard':
        return 'Dashboard';
      case 'users':
        return 'Gestão de Usuários';
      case 'administrators':
        return 'Administradores';
      case 'validation':
        return 'Validação de Usuários';
      case 'reports':
        return 'Relatórios';
      case 'rankings':
        return 'Rankings';
      default:
        return 'Dashboard';
    }
  }

  String _getPageSubtitle(String currentRoute) {
    switch (currentRoute) {
      case 'dashboard':
        return 'Visão geral da plataforma Achei Saúde';
      case 'users':
        return 'Gerencie profissionais de saúde e pacientes da plataforma';
      case 'administrators':
        return 'Gerencie contas de administradores e suas permissões';
      case 'validation':
        return 'Valide documentos e certificados de profissionais e pacientes';
      case 'reports':
        return 'Visualize métricas e dados de desempenho da plataforma';
      case 'rankings':
        return 'Acompanhe as profissões mais procuradas e profissionais com melhor desempenho';
      default:
        return 'Visão geral da plataforma Achei Saúde';
    }
  }

  void _showAdminCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => const AdminCreateDialog(),
    );
  }

  Widget _headerButton(String currentRoute) {
    switch (currentRoute) {
      case 'administrators':
        return CustomButton(
          onPressed: _showAdminCreateDialog,
          icon: const Icon(Icons.add, size: 20, color: Colors.white),
          label: 'Novo Administrador',
        );
      default:
        return Container();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NavigationService>(
      builder: (context, navigationService, child) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundWhite,
          body: Row(
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: _isSidebarOpen ? 250 : 0,
                child: ClipRect(
                  child: Sidebar(
                    currentRoute: navigationService.currentRoute,
                    onNavigate: navigationService.navigateToRoute,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    Container(
                      decoration: const BoxDecoration(
                        color: AppTheme.backgroundWhite,
                        border: Border(
                          bottom: BorderSide(color: AppTheme.borderGray),
                        ),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(
                              Icons.view_sidebar_outlined,
                              size: 20,
                              color: mutedForeground,
                            ),
                            onPressed: () {
                              setState(() {
                                _isSidebarOpen = !_isSidebarOpen;
                              });
                            },
                          ),
                          const Spacer(),
                          const LogoutButton(),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 16,
                      ),
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getPageTitle(navigationService.currentRoute),
                                style: text3xl,
                              ),
                              Text(
                                _getPageSubtitle(
                                    navigationService.currentRoute),
                                style: textSm,
                              ),
                            ],
                          ),
                          _headerButton(navigationService.currentRoute),
                        ],
                      ),
                    ),
                    Expanded(
                      child: _buildContent(navigationService.currentRoute),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
