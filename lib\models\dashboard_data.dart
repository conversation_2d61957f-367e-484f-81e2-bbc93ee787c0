class DashboardData {
  final int totalUsers;
  final double userGrowth;
  final int pendingValidations;
  final double validationChange;
  final int todayAppointments;
  final double appointmentGrowth;
  final double approvalRate;
  final double approvalChange;
  final List<Activity> recentActivities;
  final List<Notification> notifications;

  DashboardData({
    required this.totalUsers,
    required this.userGrowth,
    required this.pendingValidations,
    required this.validationChange,
    required this.todayAppointments,
    required this.appointmentGrowth,
    required this.approvalRate,
    required this.approvalChange,
    required this.recentActivities,
    required this.notifications,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      totalUsers: json['totalUsers'],
      userGrowth: json['userGrowth'].toDouble(),
      pendingValidations: json['pendingValidations'],
      validationChange: json['validationChange'].toDouble(),
      todayAppointments: json['todayAppointments'],
      appointmentGrowth: json['appointmentGrowth'].toDouble(),
      approvalRate: json['approvalRate'].toDouble(),
      approvalChange: json['approvalChange'].toDouble(),
      recentActivities: (json['recentActivities'] as List)
          .map((item) => Activity.fromJson(item))
          .toList(),
      notifications: (json['notifications'] as List)
          .map((item) => Notification.fromJson(item))
          .toList(),
    );
  }
}

class Activity {
  final String id;
  final String description;
  final DateTime timestamp;
  final ActivityStatus status;

  Activity({
    required this.id,
    required this.description,
    required this.timestamp,
    required this.status,
  });

  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'],
      description: json['description'],
      timestamp: DateTime.parse(json['timestamp']),
      status: ActivityStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
    );
  }
}

class Notification {
  final String id;
  final String title;
  final String description;
  final NotificationType type;
  final DateTime timestamp;

  Notification({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.timestamp,
  });

  factory Notification.fromJson(Map<String, dynamic> json) {
    return Notification(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: NotificationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

enum ActivityStatus { approved, rejected, pending, completed, newItem }

enum NotificationType { urgent, info, success }
