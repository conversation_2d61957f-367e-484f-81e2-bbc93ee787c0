import 'package:flutter_bloc/flutter_bloc.dart';

import '../login_service.dart';
import 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  final LoginService _loginService;

  LoginCubit({LoginService? loginService})
      : _loginService = loginService ?? LoginService(),
        super(const LoginState());

  /// Realiza login com usuário e senha
  Future<void> loginWithUsername({
    required String username,
    required String password,
  }) async {
    if (username.trim().isEmpty || password.trim().isEmpty) {
      emit(state.copyWith(
        status: LoginStatus.failure,
        errorMessage: 'Usuário e senha são obrigatórios',
      ));
      return;
    }

    emit(state.copyWith(status: LoginStatus.loading));

    try {
      final result = await _loginService.loginWithUsername(
        username: username.trim(),
        password: password,
      );

      if (result.isSuccess && result.user != null) {
        emit(state.copyWith(
          status: LoginStatus.success,
          user: result.user,
          errorMessage: null,
        ));
      } else {
        emit(state.copyWith(
          status: LoginStatus.failure,
          errorMessage: result.errorMessage ?? 'Erro desconhecido no login',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: LoginStatus.failure,
        errorMessage: 'Erro inesperado: $e',
      ));
    }
  }

  /// Realiza login com email e senha (método legado)
  Future<void> login({
    required String email,
    required String password,
  }) async {
    if (email.trim().isEmpty || password.trim().isEmpty) {
      emit(state.copyWith(
        status: LoginStatus.failure,
        errorMessage: 'Email e senha são obrigatórios',
      ));
      return;
    }

    emit(state.copyWith(status: LoginStatus.loading));

    try {
      final result = await _loginService.login(
        email: email.trim(),
        password: password,
      );

      if (result.isSuccess && result.user != null) {
        emit(state.copyWith(
          status: LoginStatus.success,
          user: result.user,
          errorMessage: null,
        ));
      } else {
        emit(state.copyWith(
          status: LoginStatus.failure,
          errorMessage: result.errorMessage ?? 'Erro desconhecido no login',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: LoginStatus.failure,
        errorMessage: 'Erro inesperado: $e',
      ));
    }
  }

  /// Realiza logout
  Future<void> logout() async {
    try {
      await _loginService.logout();
      emit(const LoginState()); // Reset para estado inicial
    } catch (e) {
      emit(state.copyWith(
        status: LoginStatus.failure,
        errorMessage: 'Erro ao fazer logout: $e',
      ));
    }
  }

  /// Envia email de redefinição de senha
  Future<void> sendPasswordResetEmail(String email) async {
    if (email.trim().isEmpty) {
      emit(state.copyWith(
        status: LoginStatus.forgotPasswordFailure,
        errorMessage: 'Email é obrigatório',
      ));
      return;
    }

    emit(state.copyWith(status: LoginStatus.forgotPasswordLoading));

    try {
      await _loginService.sendPasswordResetEmail(email.trim());
      emit(state.copyWith(
        status: LoginStatus.forgotPasswordSuccess,
        successMessage: 'Email de redefinição enviado com sucesso!',
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: LoginStatus.forgotPasswordFailure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Alterna visibilidade da senha
  void togglePasswordVisibility() {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  /// Limpa mensagens de erro e sucesso
  void clearMessages() {
    emit(state.clearMessages());
  }

  /// Reset para estado inicial
  void reset() {
    emit(const LoginState());
  }

  /// Verifica se há usuário logado
  Future<bool> checkAuthStatus() async {
    try {
      final isLoggedIn = await _loginService.isLoggedIn();
      final currentUser = _loginService.getCurrentUser();

      if (isLoggedIn && currentUser != null) {
        emit(state.copyWith(
          status: LoginStatus.success,
          user: currentUser,
        ));
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
