import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';

class TextFieldCustom extends StatefulWidget {
  final String? errorMessage;
  final String? hintText;
  final bool isPassword;
  final bool isEnabled;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputAction? textInputAction;
  final String? initialValue;
  final TextEditingController? controller;
  final int? maxLines;
  final Function(String)? onSubmit;
  final Widget? suffixIcon;
  final String? label;

  const TextFieldCustom({
    super.key,
    this.errorMessage,
    this.hintText,
    this.isPassword = false,
    this.isEnabled = true,
    this.validator,
    this.onChanged,
    this.keyboardType,
    this.inputFormatters,
    this.textInputAction,
    this.initialValue,
    this.controller,
    this.maxLines = 1,
    this.onSubmit,
    this.suffixIcon,
    this.label,
  });

  @override
  State<TextFieldCustom> createState() => _TextFieldCustomState();
}

class _TextFieldCustomState extends State<TextFieldCustom> {
  late bool _obscureText;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.isPassword;
  }

  void _toggleVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: textSm.copyWith(color: foreground),
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          obscureText: _obscureText,
          controller: widget.controller,
          enabled: widget.isEnabled,
          validator: widget.validator,
          maxLines: widget.maxLines,
          initialValue: widget.initialValue,
          onChanged: widget.onChanged,
          onFieldSubmitted: widget.onSubmit,
          keyboardType: widget.keyboardType,
          inputFormatters: widget.inputFormatters,
          textInputAction: widget.textInputAction,
          style: textSm.copyWith(color: foreground),
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: borderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppTheme.primaryGreen),
            ),
            hintText: widget.hintText,
            hintStyle: textSm,
            suffixIcon: widget.suffixIcon ??
                (widget.isPassword
                    ? Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: IconButton(
                          onPressed: _toggleVisibility,
                          icon: Icon(
                            _obscureText
                                ? Icons.visibility_off_outlined
                                : Icons.visibility_outlined,
                            color: foreground,
                          ),
                        ),
                      )
                    : null),
          ),
        ),
        if (widget.errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              widget.errorMessage!,
              style: textSm.copyWith(color: AppTheme.errorRed),
            ),
          ),
      ],
    );
  }
}
