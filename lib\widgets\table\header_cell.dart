import 'package:flutter/material.dart';

import '../../utils/app_text_theme.dart';
import '../../utils/app_theme.dart';

class HeaderCell extends StatelessWidget {
  final String label;
  final double? width;

  const HeaderCell({
    super.key,
    required this.label,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        height: 48,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        color: AppTheme.backgroundGray,
        child: Text(
          label,
          style: textSm.copyWith(
            color: AppTheme.textGray,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
