import 'package:flutter/material.dart';

import '../utils/app_theme.dart';
import '../widgets/stat_card.dart';

class ReportsContent extends StatefulWidget {
  const ReportsContent({super.key});

  @override
  State<ReportsContent> createState() => _ReportsContentState();
}

class _ReportsContentState extends State<ReportsContent> {
  String _selectedPeriod = 'Todos os dados';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header com filtros
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  _buildPeriodChip('Todos os dados'),
                  const SizedBox(width: 12),
                  _buildPeriodChip('Último mês'),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Implementar exportação
                },
                icon: const Icon(Icons.download),
                label: const Text('Exportar'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorRed,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Cards de estatísticas
          const Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Total de Usuários',
                  value: '1,247',
                  change: '+67 este mês',
                  changeType: ChangeType.positive,
                  subtitle: '',
                  description: '',
                  icon: Icons.people,
                  iconColor: AppTheme.primaryBlue,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Profissionais',
                  value: '324',
                  change: '+15 este mês',
                  changeType: ChangeType.positive,
                  subtitle: '',
                  description: '',
                  icon: Icons.medical_services,
                  iconColor: AppTheme.primaryBlue,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Pacientes',
                  value: '923',
                  change: '+52 este mês',
                  changeType: ChangeType.positive,
                  subtitle: '',
                  description: '',
                  icon: Icons.person,
                  iconColor: AppTheme.primaryBlue,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Atendimentos',
                  value: '2,856',
                  change: '+387 este mês',
                  changeType: ChangeType.positive,
                  subtitle: '',
                  description: '',
                  icon: Icons.calendar_today,
                  iconColor: AppTheme.primaryBlue,
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Gráficos
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Cadastros por período
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Cadastros por Período',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Evolução de cadastros de profissionais e pacientes',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: AppTheme.textGray.withValues(alpha: 0.7),
                              ),
                        ),
                        const SizedBox(height: 24),

                        // Placeholder para gráfico de barras
                        Container(
                          height: 200,
                          decoration: BoxDecoration(
                            color: AppTheme.backgroundGray,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppTheme.borderGray),
                          ),
                          child: const Center(
                            child: Text(
                              'Gráfico de Barras\n(Cadastros por Período)',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: AppTheme.textGray,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 24),

              // Profissionais por especialidade
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Profissionais por Especialidade',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Distribuição de profissionais cadastrados por área',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: AppTheme.textGray.withValues(alpha: 0.7),
                              ),
                        ),
                        const SizedBox(height: 24),

                        // Placeholder para gráfico de pizza
                        Container(
                          height: 200,
                          decoration: BoxDecoration(
                            color: AppTheme.backgroundGray,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppTheme.borderGray),
                          ),
                          child: const Center(
                            child: Text(
                              'Gráfico de Pizza\n(Especialidades)',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: AppTheme.textGray,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Tendência de atendimentos
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tendência de Atendimentos',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Total de atendimentos agendados vs concluídos por período',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                  ),
                  const SizedBox(height: 24),

                  // Placeholder para gráfico de linha
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundGray,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppTheme.borderGray),
                    ),
                    child: const Center(
                      child: Text(
                        'Gráfico de Linha\n(Tendência de Atendimentos)',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: AppTheme.textGray,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 32),

          // Estatísticas detalhadas
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Estatísticas Detalhadas',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Resumo completo das métricas da plataforma',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      // USUÁRIOS
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'USUÁRIOS',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textGray,
                                  ),
                            ),
                            const SizedBox(height: 16),
                            _buildStatRow('Total Ativo:', '1247'),
                            _buildStatRow('Profissionais:', '324'),
                            _buildStatRow('Pacientes:', '923'),
                          ],
                        ),
                      ),

                      // ATENDIMENTOS
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'ATENDIMENTOS',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textGray,
                                  ),
                            ),
                            const SizedBox(height: 16),
                            _buildStatRow('Total:', '2856'),
                            _buildStatRow('Concluídos:', '2643'),
                            _buildStatRow('Pendentes:', '213'),
                          ],
                        ),
                      ),

                      // CRESCIMENTO
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'CRESCIMENTO',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textGray,
                                  ),
                            ),
                            const SizedBox(height: 16),
                            _buildStatRow('Taxa de Conclusão:', '92.5%'),
                            _buildStatRow('Novos Usuários/Mês:', '67'),
                            _buildStatRow('Atendimentos/Mês:', '387'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodChip(String label) {
    final isSelected = _selectedPeriod == label;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedPeriod = label;
        });
      },
      selectedColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryBlue,
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textGray,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppTheme.textGray,
            ),
          ),
        ],
      ),
    );
  }
}
