import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/utils/extensions.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

class PersonalDataCard extends StatelessWidget {
  final String name;
  final String? additionalInfo;
  final DocumentStatus status;
  final VoidCallback onApprove;
  final VoidCallback onReject;
  final String? rejectionReason;

  const PersonalDataCard({
    super.key,
    required this.name,
    this.additionalInfo,
    required this.status,
    required this.onApprove,
    required this.onReject,
    this.rejectionReason,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(name,
                          style: const TextStyle(fontWeight: FontWeight.bold)),
                      if (additionalInfo != null) Text(additionalInfo!),
                    ],
                  ),
                ),
                TagWidget(
                  title: status.displayName,
                  type: status.tagType,
                ),
              ],
            ),
            if (rejectionReason != null) ...[
              const SizedBox(height: 8),
              Text('Motivo: $rejectionReason',
                  style: const TextStyle(color: Colors.red)),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed:
                      status == DocumentStatus.approved ? null : onApprove,
                  icon: const Icon(Icons.check),
                  label: const Text('Aprovar'),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed:
                      status == DocumentStatus.rejected ? null : onReject,
                  icon: const Icon(Icons.close),
                  label: const Text('Rejeitar'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
