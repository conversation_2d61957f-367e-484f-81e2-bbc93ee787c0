import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/widgets/app_logo_widget.dart';
import 'package:provider/provider.dart';

import '../services/firebase_auth_service.dart';
import '../utils/app_theme.dart';

class Sidebar extends StatelessWidget {
  final String currentRoute;
  final Function(String) onNavigate;

  const Sidebar({
    super.key,
    required this.currentRoute,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      decoration: const BoxDecoration(
        border: Border(
          right: BorderSide(color: AppTheme.borderGray),
        ),
        color: sideBarBackground,
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppTheme.borderGray),
              ),
            ),
            child: const AppLogoWidget(),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              children: [
                _buildMenuItem(
                  context,
                  icon: Icons.dashboard_outlined,
                  activeIcon: Icons.dashboard,
                  title: 'Dashboard',
                  route: 'dashboard',
                  badge: null,
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.people_outline,
                  activeIcon: Icons.people,
                  title: 'Gestão de Usuários',
                  route: 'users',
                  badge: null,
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.admin_panel_settings_outlined,
                  activeIcon: Icons.admin_panel_settings,
                  title: 'Administradores',
                  route: 'administrators',
                  badge: null,
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.verified_user_outlined,
                  activeIcon: Icons.verified_user,
                  title: 'Validação de Usuários',
                  route: 'validation',
                  badge: '12',
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.analytics_outlined,
                  activeIcon: Icons.analytics,
                  title: 'Relatórios',
                  route: 'reports',
                  badge: null,
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.leaderboard_outlined,
                  activeIcon: Icons.leaderboard,
                  title: 'Rankings',
                  route: 'rankings',
                  badge: null,
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: AppTheme.borderGray),
              ),
            ),
            child: Consumer<FirebaseAuthService>(
              builder: (context, authService, _) {
                return Column(
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 16,
                          backgroundColor: AppTheme.primaryPurple,
                          child: Text(
                            (authService.userName?.isNotEmpty == true
                                    ? authService.userName!.substring(0, 1)
                                    : authService.userEmail?.substring(0, 1) ??
                                        'A')
                                .toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            authService.userName?.isNotEmpty == true
                                ? authService.userName!
                                : authService.userEmail ?? 'Admin',
                            style: textSm,
                            maxLines: 1,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.logout, size: 20),
                          onPressed: () async {
                            await authService.signOut();
                            if (context.mounted) {
                              Navigator.of(context)
                                  .pushReplacementNamed('/login');
                            }
                          },
                          tooltip: 'Sair',
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required IconData activeIcon,
    required String title,
    required String route,
    String? badge,
  }) {
    final isActive = currentRoute == route;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => onNavigate(route),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              color: isActive
                  ? AppTheme.primaryGreen.withValues(alpha: 0.1)
                  : null,
              borderRadius: BorderRadius.circular(8),
              border: isActive
                  ? Border.all(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.3))
                  : null,
            ),
            child: Row(
              children: [
                Icon(
                  isActive ? activeIcon : icon,
                  size: 20,
                  color: isActive ? AppTheme.primaryGreen : AppTheme.textGray,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    maxLines: 2,
                    style: textSm.copyWith(
                      color:
                          isActive ? AppTheme.primaryGreen : AppTheme.textGray,
                      fontWeight:
                          isActive ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
                if (badge != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppTheme.errorRed,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      badge,
                      style: textXs.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
