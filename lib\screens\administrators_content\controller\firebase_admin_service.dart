import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../models/admin.dart';
import '../../../utils/enums.dart';

class FirebaseAdminService {
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;

  FirebaseAdminService({
    FirebaseAuth? auth,
    FirebaseFirestore? firestore,
  })  : _auth = auth ?? FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance;

  /// Cria um novo administrador no Firebase Auth e Firestore
  Future<AdminCreationResult> createAdmin({
    required String name,
    required String username,
    required String email,
    required String password,
    required AdmEnum role,
    String? phone,
    String location = 'Sede',
  }) async {
    try {
      // 1. Criar usuário no Firebase Auth
      final UserCredential userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = userCredential.user;
      if (user == null) {
        return AdminCreationResult.failure('Falha ao criar usuário no Firebase Auth');
      }

      // 2. Atualizar o displayName do usuário
      await user.updateDisplayName(name);

      // 3. Criar documento do administrador no Firestore
      final admin = Admin(
        id: user.uid,
        name: name,
        email: email,
        phone: phone ?? '',
        type: 'Admin',
        location: location,
        status: UserStatusEnum.active,
        appointments: 0,
        lastAccess: DateTime.now(),
        documents: [],
        username: username,
        role: role,
        createdAt: DateTime.now(),
      );

      await _firestore.collection('administrators').doc(user.uid).set(admin.toJson());

      // 4. Criar documento na collection de usuários com tipo de administrador
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'name': name,
        'email': email,
        'phone': phone ?? '',
        'userType': 'administrator',
        'adminRole': role.toString().split('.').last,
        'username': username,
        'status': 'active',
        'createdAt': FieldValue.serverTimestamp(),
        'lastAccess': FieldValue.serverTimestamp(),
      });

      return AdminCreationResult.success(admin);
    } on FirebaseAuthException catch (e) {
      return AdminCreationResult.failure(_handleFirebaseAuthException(e));
    } catch (e) {
      return AdminCreationResult.failure('Erro inesperado: $e');
    }
  }

  /// Busca todos os administradores do Firestore
  Future<List<Admin>> getAdministrators() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('administrators')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Admin.fromJson({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erro ao buscar administradores: $e');
    }
  }

  /// Atualiza um administrador no Firestore
  Future<void> updateAdmin(Admin admin) async {
    try {
      await _firestore.collection('administrators').doc(admin.id).update(admin.toJson());
      
      // Atualiza também na collection de usuários
      await _firestore.collection('users').doc(admin.id).update({
        'name': admin.name,
        'email': admin.email,
        'phone': admin.phone,
        'adminRole': admin.role.toString().split('.').last,
        'username': admin.username,
        'status': admin.status.toString().split('.').last,
        'lastAccess': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Erro ao atualizar administrador: $e');
    }
  }

  /// Atualiza o status de um administrador
  Future<void> updateAdminStatus(String adminId, UserStatusEnum status) async {
    try {
      await _firestore.collection('administrators').doc(adminId).update({
        'status': status.toString().split('.').last,
        'lastAccess': FieldValue.serverTimestamp(),
      });

      await _firestore.collection('users').doc(adminId).update({
        'status': status.toString().split('.').last,
        'lastAccess': FieldValue.serverTimestamp(),
      });

      // Se estiver desabilitando, também desabilita no Firebase Auth
      if (status == UserStatusEnum.inactive || status == UserStatusEnum.suspended) {
        // Nota: Para desabilitar usuários no Firebase Auth, você precisa usar o Admin SDK
        // que não está disponível no cliente. Isso seria feito no backend.
      }
    } catch (e) {
      throw Exception('Erro ao atualizar status do administrador: $e');
    }
  }

  /// Deleta um administrador (soft delete)
  Future<void> deleteAdmin(String adminId) async {
    try {
      await updateAdminStatus(adminId, UserStatusEnum.inactive);
    } catch (e) {
      throw Exception('Erro ao deletar administrador: $e');
    }
  }

  /// Busca um administrador por ID
  Future<Admin?> getAdminById(String adminId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection('administrators')
          .doc(adminId)
          .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Admin.fromJson({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erro ao buscar administrador: $e');
    }
  }

  /// Verifica se um email já está em uso
  Future<bool> isEmailInUse(String email) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('administrators')
          .where('email', isEqualTo: email)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Verifica se um username já está em uso
  Future<bool> isUsernameInUse(String username) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('administrators')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Trata exceções específicas do Firebase Auth
  String _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'email-already-in-use':
        return 'Este email já está sendo usado por outra conta.';
      case 'invalid-email':
        return 'Email inválido. Verifique o formato do email.';
      case 'operation-not-allowed':
        return 'Operação não permitida. Entre em contato com o suporte.';
      case 'weak-password':
        return 'A senha é muito fraca. Use uma senha mais forte.';
      case 'network-request-failed':
        return 'Erro de conexão. Verifique sua internet e tente novamente.';
      default:
        return 'Erro de autenticação: ${e.message ?? 'Erro desconhecido'}';
    }
  }
}

/// Classe para representar o resultado da criação de administrador
class AdminCreationResult {
  final bool isSuccess;
  final Admin? admin;
  final String? errorMessage;

  AdminCreationResult._({
    required this.isSuccess,
    this.admin,
    this.errorMessage,
  });

  factory AdminCreationResult.success(Admin admin) {
    return AdminCreationResult._(
      isSuccess: true,
      admin: admin,
    );
  }

  factory AdminCreationResult.failure(String errorMessage) {
    return AdminCreationResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }
}
