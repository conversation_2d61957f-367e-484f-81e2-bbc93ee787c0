import 'package:flutter/material.dart';

import '../utils/app_theme.dart';
import 'dashboard_content.dart';

class NotificationCard extends StatelessWidget {
  final String title;
  final String description;
  final NotificationType type;
  final IconData icon;

  const NotificationCard({
    super.key,
    required this.title,
    required this.description,
    required this.type,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getBorderColor()),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getIconColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: _getIconColor(),
            ),
          ),

          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: _getTextColor(),
                            ),
                      ),
                    ),
                    if (type == NotificationType.urgent)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppTheme.errorRed,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'Urgente',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getTextColor().withValues(alpha: 0.8),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case NotificationType.urgent:
        return Colors.red.shade50;
      case NotificationType.info:
        return Colors.blue.shade50;
      case NotificationType.success:
        return Colors.green.shade50;
    }
  }

  Color _getBorderColor() {
    switch (type) {
      case NotificationType.urgent:
        return Colors.red.shade200;
      case NotificationType.info:
        return Colors.blue.shade200;
      case NotificationType.success:
        return Colors.green.shade200;
    }
  }

  Color _getIconColor() {
    switch (type) {
      case NotificationType.urgent:
        return AppTheme.errorRed;
      case NotificationType.info:
        return AppTheme.primaryBlue;
      case NotificationType.success:
        return AppTheme.primaryGreen;
    }
  }

  Color _getTextColor() {
    switch (type) {
      case NotificationType.urgent:
        return Colors.red.shade800;
      case NotificationType.info:
        return Colors.blue.shade800;
      case NotificationType.success:
        return Colors.green.shade800;
    }
  }
}
