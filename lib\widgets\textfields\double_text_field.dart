import 'package:flutter/services.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

class DoubleTextField extends TextFieldCustom {
  DoubleTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.hintText,
    super.textInputAction,
    super.initialValue,
    super.controller,
    super.validator,
    super.isEnabled,
    List<TextInputFormatter>? inputFormatters,
  }) : super(
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            if (inputFormatters != null) ...inputFormatters,
            FilteringTextInputFormatter.allow(
              RegExp(r'^\d*([.,]\d*)?$'),
            ),
          ],
        );
}
