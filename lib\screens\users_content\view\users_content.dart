import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homecare_nexus_clone/core/global_instances.dart';
import 'package:homecare_nexus_clone/screens/users_content/view/widgets/user_content_row.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/widgets/containers/container_bordered.dart';
import 'package:homecare_nexus_clone/widgets/containers/content_container.dart';
import 'package:homecare_nexus_clone/widgets/filter_chip_widget.dart';

import '../../../utils/app_theme.dart';
import '../../../widgets/stat_card.dart';
import '../controller/cubits/users_content_state.dart';

class UsersContent extends StatefulWidget {
  const UsersContent({super.key});

  @override
  State<UsersContent> createState() => _UsersContentState();
}

class _UsersContentState extends State<UsersContent> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder(
      bloc: userContentCubit,
      builder: (context, UserContentState state) {
        final total = state.all.length;
        final profissionais = state.all
            .where((u) => u.type.toLowerCase() == 'profissional')
            .length;
        final pacientes =
            state.all.where((u) => u.type.toLowerCase() == 'paciente').length;
        final ativos =
            state.all.where((u) => u.status == UserStatusEnum.active).length;
        final inativos =
            state.all.where((u) => u.status == UserStatusEnum.inactive).length;
        final suspensos =
            state.all.where((u) => u.status == UserStatusEnum.suspended).length;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: StatCard(
                      title: 'Total',
                      value: total.toString(),
                      valueColor: AppTheme.textGray,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Profissionais',
                      value: profissionais.toString(),
                      valueColor: AppTheme.primaryGreen,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Pacientes',
                      value: pacientes.toString(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Ativos',
                      value: ativos.toString(),
                      valueColor: AppTheme.primaryGreen,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Inativos',
                      value: inativos.toString(),
                      valueColor: Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Suspensos',
                      value: suspensos.toString(),
                      valueColor: AppTheme.errorRed,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              ContentContainer(
                title: 'Lista de Usuários',
                subtitle:
                    'Visualize e gerencie todos os usuários cadastrados na plataforma',
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextField(
                          controller: _searchController,
                          onChanged: (v) =>
                              userContentCubit.applyFilter(search: v),
                          decoration: InputDecoration(
                            hintText: 'Buscar por nome ou email...',
                            hintStyle: textSm.copyWith(
                              color: foreground,
                              fontWeight: FontWeight.w400,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(
                                  color: AppTheme.primaryGreen),
                            ),
                            prefixIcon: const Icon(Icons.search),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      InkWell(
                        onTap: () {
                          // TODO: Implementar ações de filtros avançados
                        },
                        child: ContainerBordered(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          child: const Row(
                            children: [
                              Icon(
                                Icons.filter_alt_outlined,
                                color: mutedForeground,
                              ),
                              Text('Filtros', style: textSm)
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: background,
                    ),
                    child: Row(
                      children: [
                        FilterChipWidget(
                          label: 'Todos ($total)',
                          value: 'Todos',
                          isSelected: state.filter == 'Todos',
                          onSelected: (selected) =>
                              userContentCubit.applyFilter(filter: selected),
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label: 'Profissionais ($profissionais)',
                          value: 'Profissional',
                          isSelected: state.filter == 'Profissionais',
                          onSelected: (selected) =>
                              userContentCubit.applyFilter(filter: selected),
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label: 'Pacientes ($pacientes)',
                          value: 'Paciente',
                          isSelected: state.filter == 'Pacientes',
                          onSelected: (selected) =>
                              userContentCubit.applyFilter(filter: selected),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: const BoxDecoration(
                          color: AppTheme.backgroundGray,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(8),
                            topRight: Radius.circular(8),
                          ),
                        ),
                        child: const Row(
                          children: [
                            Expanded(
                                flex: 3,
                                child: Text('Usuário',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 1,
                                child: Text('Tipo',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 2,
                                child: Text('Especialidade',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 2,
                                child: Text('Localização',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 1,
                                child: Text('Status',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 1,
                                child: Text('Atendimentos',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 1,
                                child: Text('Último Acesso',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                            Expanded(
                                flex: 1,
                                child: Text('Ações',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600))),
                          ],
                        ),
                      ),
                      Column(
                        children: state.filtered.map((user) {
                          return UserContentRow(userModel: user);
                        }).toList(),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
