import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'login_repository.dart';

class LoginService {
  final LoginRepository _repository;

  LoginService({LoginRepository? repository})
      : _repository = repository ?? LoginRepository();

  /// Realiza login com usuário e senha
  Future<LoginResult> loginWithUsername({
    required String username,
    required String password,
  }) async {
    try {
      final user = await _repository.signInWithUsernameAndPassword(
        username: username,
        password: password,
      );

      if (user != null) {
        // Salva dados do usuário localmente
        await _saveUserDataLocally(user);
        return LoginResult.success(user);
      } else {
        return LoginResult.failure('Falha na autenticação');
      }
    } catch (e) {
      return LoginResult.failure(e.toString());
    }
  }

  /// Realiza login com email e senha (método legado)
  Future<LoginResult> login({
    required String email,
    required String password,
  }) async {
    try {
      final user = await _repository.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (user != null) {
        // Salva dados do usuário localmente
        await _saveUserDataLocally(user);
        return LoginResult.success(user);
      } else {
        return LoginResult.failure('Falha na autenticação');
      }
    } catch (e) {
      return LoginResult.failure(e.toString());
    }
  }

  /// Realiza logout e limpa dados locais
  Future<void> logout() async {
    try {
      await _repository.signOut();
      await _clearUserDataLocally();
    } catch (e) {
      throw Exception('Erro ao fazer logout: $e');
    }
  }

  /// Verifica se há um usuário logado
  User? getCurrentUser() {
    return _repository.getCurrentUser();
  }

  /// Stream para monitorar mudanças no estado de autenticação
  Stream<User?> get authStateChanges => _repository.authStateChanges;

  /// Envia email de redefinição de senha
  Future<void> sendPasswordResetEmail(String email) async {
    await _repository.sendPasswordResetEmail(email);
  }

  /// Verifica se o usuário está logado localmente
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
    final currentUser = getCurrentUser();
    return isLoggedIn && currentUser != null;
  }

  /// Salva dados do usuário localmente
  Future<void> _saveUserDataLocally(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isLoggedIn', true);
    await prefs.setString('userId', user.uid);
    await prefs.setString('userEmail', user.email ?? '');
    await prefs.setString('userName', user.displayName ?? '');
  }

  /// Limpa dados do usuário localmente
  Future<void> _clearUserDataLocally() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('isLoggedIn');
    await prefs.remove('userId');
    await prefs.remove('userEmail');
    await prefs.remove('userName');
  }

  /// Obtém dados do usuário salvos localmente
  Future<Map<String, String?>> getUserDataLocally() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'userId': prefs.getString('userId'),
      'userEmail': prefs.getString('userEmail'),
      'userName': prefs.getString('userName'),
    };
  }
}

/// Classe para representar o resultado do login
class LoginResult {
  final bool isSuccess;
  final User? user;
  final String? errorMessage;

  LoginResult._({
    required this.isSuccess,
    this.user,
    this.errorMessage,
  });

  factory LoginResult.success(User user) {
    return LoginResult._(
      isSuccess: true,
      user: user,
    );
  }

  factory LoginResult.failure(String errorMessage) {
    return LoginResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }
}
