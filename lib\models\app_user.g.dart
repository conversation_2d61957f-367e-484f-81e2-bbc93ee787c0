// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_user.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$AppUserCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// AppUser(...).copyWith(id: 12, name: "My name")
  /// ````
  AppUser call({
    String id,
    String name,
    String email,
    String phone,
    String type,
    String? specialty,
    String location,
    UserStatusEnum status,
    DocumentStatus validationStatus,
    int appointments,
    DateTime lastAccess,
    List<DocumentModel> documents,
    bool hasResponses,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfAppUser.copyWith(...)`.
class _$AppUserCWProxyImpl implements _$AppUserCWProxy {
  const _$AppUserCWProxyImpl(this._value);

  final AppUser _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// AppUser(...).copyWith(id: 12, name: "My name")
  /// ````
  AppUser call({
    Object? id = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? email = const $CopyWithPlaceholder(),
    Object? phone = const $CopyWithPlaceholder(),
    Object? type = const $CopyWithPlaceholder(),
    Object? specialty = const $CopyWithPlaceholder(),
    Object? location = const $CopyWithPlaceholder(),
    Object? status = const $CopyWithPlaceholder(),
    Object? validationStatus = const $CopyWithPlaceholder(),
    Object? appointments = const $CopyWithPlaceholder(),
    Object? lastAccess = const $CopyWithPlaceholder(),
    Object? documents = const $CopyWithPlaceholder(),
    Object? hasResponses = const $CopyWithPlaceholder(),
  }) {
    return AppUser(
      id: id == const $CopyWithPlaceholder()
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      name: name == const $CopyWithPlaceholder()
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      email: email == const $CopyWithPlaceholder()
          ? _value.email
          // ignore: cast_nullable_to_non_nullable
          : email as String,
      phone: phone == const $CopyWithPlaceholder()
          ? _value.phone
          // ignore: cast_nullable_to_non_nullable
          : phone as String,
      type: type == const $CopyWithPlaceholder()
          ? _value.type
          // ignore: cast_nullable_to_non_nullable
          : type as String,
      specialty: specialty == const $CopyWithPlaceholder()
          ? _value.specialty
          // ignore: cast_nullable_to_non_nullable
          : specialty as String?,
      location: location == const $CopyWithPlaceholder()
          ? _value.location
          // ignore: cast_nullable_to_non_nullable
          : location as String,
      status: status == const $CopyWithPlaceholder()
          ? _value.status
          // ignore: cast_nullable_to_non_nullable
          : status as UserStatusEnum,
      validationStatus: validationStatus == const $CopyWithPlaceholder()
          ? _value.validationStatus
          // ignore: cast_nullable_to_non_nullable
          : validationStatus as DocumentStatus,
      appointments: appointments == const $CopyWithPlaceholder()
          ? _value.appointments
          // ignore: cast_nullable_to_non_nullable
          : appointments as int,
      lastAccess: lastAccess == const $CopyWithPlaceholder()
          ? _value.lastAccess
          // ignore: cast_nullable_to_non_nullable
          : lastAccess as DateTime,
      documents: documents == const $CopyWithPlaceholder()
          ? _value.documents
          // ignore: cast_nullable_to_non_nullable
          : documents as List<DocumentModel>,
      hasResponses: hasResponses == const $CopyWithPlaceholder()
          ? _value.hasResponses
          // ignore: cast_nullable_to_non_nullable
          : hasResponses as bool,
    );
  }
}

extension $AppUserCopyWith on AppUser {
  /// Returns a callable class that can be used as follows: `instanceOfAppUser.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$AppUserCWProxy get copyWith => _$AppUserCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// AppUser(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  AppUser copyWithNull({
    bool specialty = false,
  }) {
    return AppUser(
      id: id,
      name: name,
      email: email,
      phone: phone,
      type: type,
      specialty: specialty == true ? null : this.specialty,
      location: location,
      status: status,
      validationStatus: validationStatus,
      appointments: appointments,
      lastAccess: lastAccess,
      documents: documents,
      hasResponses: hasResponses,
    );
  }
}
