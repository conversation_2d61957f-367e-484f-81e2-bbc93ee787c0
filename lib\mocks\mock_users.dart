import '../models/app_user.dart';
import '../utils/enums.dart';

final mockUsers = <AppUser>[
  AppUser(
    id: '1',
    name: 'Dr<PERSON>',
    email: '<EMAIL>',
    phone: '(11) 99999-9999',
    type: 'Profissional',
    specialty: 'Cardiologia',
    location: 'São Paulo, SP',
    status: UserStatusEnum.active,
    appointments: 124,
    lastAccess: DateTime.now(),
  ),
  AppUser(
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(11) 88888-8888',
    type: 'Paciente',
    specialty: null,
    location: 'Rio de Janeiro, RJ',
    status: UserStatusEnum.active,
    appointments: 5,
    lastAccess: DateTime.now(),
  ),
  AppUser(
    id: '3',
    name: 'Dr<PERSON>. <PERSON>',
    email: '<EMAIL>',
    phone: '(11) 77777-7777',
    type: 'Profissional',
    specialty: 'Dermatologia',
    location: '<PERSON><PERSON>, MG',
    status: UserStatusEnum.inactive,
    appointments: 89,
    lastAccess: DateTime.now(),
  ),
  AppUser(
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(11) 66666-6666',
    type: 'Paciente',
    specialty: null,
    location: 'Salvador, BA',
    status: UserStatusEnum.suspended,
    appointments: 2,
    lastAccess: DateTime.now(),
  ),
];
