import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/widgets/containers/content_container.dart';
import 'package:homecare_nexus_clone/widgets/filter_chip_widget.dart';

import '../../../core/global_instances.dart';
import '../../../utils/app_theme.dart';
import '../../../widgets/stat_card.dart';
import '../controller/cubits/validation_content_cubit.dart';
import '../controller/cubits/validation_content_state.dart';
import 'widgets/validation_row_widget.dart';

class ValidationContent extends StatefulWidget {
  const ValidationContent({super.key});

  @override
  State<ValidationContent> createState() => _ValidationContentState();
}

class _ValidationContentState extends State<ValidationContent> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ValidationContentCubit, ValidationContentState>(
      bloc: validationContentCubit,
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: StatCard(
                      title: 'Novos Cadastros',
                      value: _getFilterCount('Novos Cadastros').toString(),
                      valueColor: AppTheme.primaryBlue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Alterações Pendentes',
                      value: _getFilterCount('Alterações Pendentes').toString(),
                      valueColor: Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Em Análise (Você)',
                      value: _getFilterCount('Minhas Análises').toString(),
                      valueColor: AppTheme.primaryPurple,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Cadastros Recusados',
                      value: _getFilterCount('Cadastros Recusados').toString(),
                      valueColor: AppTheme.errorRed,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              ContentContainer(
                title: 'Lista de Validações',
                subtitle:
                    'Gerencie validações de documentos com controle de concorrência',
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: background,
                    ),
                    child: Row(
                      children: [
                        FilterChipWidget(
                          label:
                              'Novos Cadastros (${_getFilterCount('Novos Cadastros')})',
                          value: 'Novos Cadastros',
                          isSelected: state.selectedFilter == 'Novos Cadastros',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label:
                              'Alterações Pendentes (${_getFilterCount('Alterações Pendentes')})',
                          value: 'Alterações Pendentes',
                          isSelected:
                              state.selectedFilter == 'Alterações Pendentes',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label:
                              'Minhas Análises (${_getFilterCount('Minhas Análises')})',
                          value: 'Minhas Análises',
                          isSelected: state.selectedFilter == 'Minhas Análises',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                        const SizedBox(width: 12),
                        FilterChipWidget(
                          label:
                              'Cadastros Recusados (${_getFilterCount('Cadastros Recusados')})',
                          value: 'Cadastros Recusados',
                          isSelected:
                              state.selectedFilter == 'Cadastros Recusados',
                          onSelected: (selected) {
                            validationContentCubit.applyFilter(selected);
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  if (state.filtered.isEmpty)
                    Center(child: _buildEmptyState(state.selectedFilter))
                  else
                    Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: AppTheme.borderGray),
                            ),
                          ),
                          child: const Row(
                            children: [
                              Expanded(
                                  flex: 3,
                                  child: Text('Usuário',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 1,
                                  child: Text('Tipo',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 3,
                                  child: Text('Campos Pendentes',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 2,
                                  child: Text('Submetido em',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 1,
                                  child: Text('Status',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 2,
                                  child: Text('Ações',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                            ],
                          ),
                        ),
                        ...state.filtered
                            .map((user) => ValidationRowWidget(user: user)),
                      ],
                    ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  int _getFilterCount(String filter) {
    final state = validationContentCubit.state;
    return validationContentCubit.service
        .filterValidations(state.all, filter)
        .length;
  }

  String _getEmptyStateMessage(String filter) {
    switch (filter) {
      case 'Novos Cadastros':
        return 'Nenhum novo cadastro pendente\nTodos os cadastros foram processados!';
      case 'Alterações Pendentes':
        return 'Nenhuma alteração pendente\nTodas as alterações foram processadas!';
      case 'Minhas Análises':
        return 'Nenhuma análise em andamento\nVocê não possui validações em progresso!';
      case 'Cadastros Recusados':
        return 'Nenhum cadastro recusado\nTodos os cadastros estão aprovados!';
      default:
        return 'Nenhum resultado encontrado\nTente aplicar um filtro diferente!';
    }
  }

  Widget _buildEmptyState(String filter) {
    return Container(
      padding: const EdgeInsets.all(48),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: AppTheme.textGray.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _getEmptyStateMessage(filter),
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textGray,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
