import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/models/app_user.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';
import 'package:homecare_nexus_clone/utils/date_formatter.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/utils/extensions.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

class UserDetailsScreen extends StatelessWidget {
  final AppUser user;

  const UserDetailsScreen({super.key, required this.user});

  String _formatDate(DateTime date) {
    return DateFormatter.formatDateTime(date);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Detalhes - ${user.name}'),
        backgroundColor: AppTheme.primaryGreen,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 28,
                  backgroundColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                  child: Icon(
                    user.type == 'Profissional'
                        ? Icons.medical_services
                        : Icons.person,
                    size: 32,
                    color: AppTheme.primaryGreen,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(user.name, style: textXl),
                      const SizedBox(height: 4),
                      Text(user.email,
                          style: textSm.copyWith(color: AppTheme.textGray)),
                      const SizedBox(height: 2),
                      Text(user.phone,
                          style: textSm.copyWith(color: AppTheme.textGray)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                TagWidget(title: user.type),
                const SizedBox(width: 8),
                TagWidget(
                  title: user.status.displayName,
                  type: user.status.tagType,
                )
              ],
            ),
            const Divider(height: 32),
            const Text('Localização', style: textLg),
            const SizedBox(height: 6),
            Text(user.location, style: textSm),
            const SizedBox(height: 16),
            if (user.specialty != null) ...[
              const Text('Especialidade', style: textLg),
              const SizedBox(height: 6),
              Text(user.specialty!, style: textSm),
              const SizedBox(height: 16),
            ],
            const Text('Consultas', style: textLg),
            const SizedBox(height: 6),
            Text('${user.appointments} consultas realizadas', style: textSm),
            const SizedBox(height: 16),
            const Text('Último Acesso', style: textLg),
            const SizedBox(height: 6),
            Text(_formatDate(user.lastAccess), style: textSm),
            const SizedBox(height: 16),
            const Text('Documentos e Certificados', style: textLg),
            const SizedBox(height: 8),
            if (user.documents.isEmpty)
              const Text('Nenhum documento cadastrado', style: textSm)
            else
              Column(
                children: user.documents.map((d) {
                  final color = {
                    DocumentStatus.approved: Colors.green,
                    DocumentStatus.rejected: Colors.red,
                    DocumentStatus.pending: Colors.orange,
                  }[d.status]!;
                  final icon = {
                    DocumentStatus.approved: Icons.check_circle,
                    DocumentStatus.rejected: Icons.cancel,
                    DocumentStatus.pending: Icons.hourglass_empty,
                  }[d.status]!;
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(icon, color: color),
                      title: Text(d.title),
                      subtitle: Text(
                        d.status?.displayName ?? 'sem status',
                        style: textSm.copyWith(color: AppTheme.textGray),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: () {},
                            icon: const Icon(Icons.file_download_outlined),
                          ),
                          IconButton(
                            onPressed: () {},
                            icon: const Icon(Icons.visibility_outlined),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }
}
