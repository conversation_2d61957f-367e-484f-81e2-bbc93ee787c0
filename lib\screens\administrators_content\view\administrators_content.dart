import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homecare_nexus_clone/core/global_instances.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/view/widgets/admin_row.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/widgets/containers/content_container.dart';

import '../../../utils/app_theme.dart';
import '../../../widgets/stat_card.dart';
import '../../administrators_content/controller/cubits/administrators_content_cubit.dart';
import '../../administrators_content/controller/cubits/administrators_content_state.dart';

class AdministratorsContent extends StatefulWidget {
  const AdministratorsContent({super.key});

  @override
  State<AdministratorsContent> createState() => _AdministratorsContentState();
}

class _AdministratorsContentState extends State<AdministratorsContent> {
  @override
  void initState() {
    super.initState();
    administratorsContentCubit.loadAdmins();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdministratorsContentCubit, AdministratorsContentState>(
      bloc: administratorsContentCubit,
      builder: (context, state) {
        final total = state.all.length;
        final active =
            state.all.where((a) => a.status == UserStatusEnum.active).length;
        final superAdmins =
            state.all.where((a) => a.role == AdmEnum.superAdmin).length;
        final admins = state.all.where((a) => a.role == AdmEnum.admin).length;
        final moderators =
            state.all.where((a) => a.role == AdmEnum.moderator).length;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: StatCard(
                      title: 'Total',
                      value: total.toString(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Ativos',
                      value: active.toString(),
                      valueColor: AppTheme.primaryGreen,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Super Admins',
                      value: superAdmins.toString(),
                      valueColor: AppTheme.primaryGreen,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Administradores',
                      value: admins.toString(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StatCard(
                      title: 'Moderadores',
                      value: moderators.toString(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              ContentContainer(
                title: 'Lista de Administradores',
                subtitle:
                    'Gerencie contas de administradores, suas funções e permissões',
                children: [
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.borderGray),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: const BoxDecoration(
                            color: AppTheme.backgroundGray,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: const Row(
                            children: [
                              Expanded(
                                  flex: 3,
                                  child: Text('Administrador',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 1,
                                  child: Text('Função',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 1,
                                  child: Text('Status',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 2,
                                  child: Text('Criado em',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 2,
                                  child: Text('Último Login',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 1,
                                  child: Text('Ações',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                            ],
                          ),
                        ),
                        ...state.all.map((a) => AdminRow(
                              admin: a,
                              roleColor: AppTheme.primaryBlue,
                              statusColor: a.status == UserStatusEnum.active
                                  ? AppTheme.primaryGreen
                                  : Colors.orange,
                              avatarColor: AppTheme.primaryBlue,
                            ))
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
