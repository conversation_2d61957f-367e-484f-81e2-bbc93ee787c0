import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:homecare_nexus_clone/services/firebase_auth_service.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';

class LogoutButton extends StatelessWidget {
  final bool showText;
  final IconData? icon;
  final String? text;

  const LogoutButton({
    super.key,
    this.showText = true,
    this.icon,
    this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<FirebaseAuthService>(
      builder: (context, authService, _) {
        return PopupMenuButton<String>(
          icon: Icon(
            icon ?? Icons.account_circle,
            color: AppTheme.primaryGreen,
          ),
          onSelected: (value) async {
            if (value == 'logout') {
              await _showLogoutDialog(context, authService);
            }
          },
          itemBuilder: (BuildContext context) => [
            PopupMenuItem<String>(
              enabled: false,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    authService.userName ?? 'Usuário',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    authService.userEmail ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const Divider(),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: AppTheme.errorRed),
                  SizedBox(width: 8),
                  Text('Sair'),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showLogoutDialog(
    BuildContext context,
    FirebaseAuthService authService,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmar Logout'),
          content: const Text('Tem certeza que deseja sair da aplicação?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorRed,
              ),
              child: const Text(
                'Sair',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );

    if (result == true) {
      try {
        await authService.signOut();
        if (context.mounted) {
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/login',
            (route) => false,
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erro ao fazer logout: $e'),
              backgroundColor: AppTheme.errorRed,
            ),
          );
        }
      }
    }
  }
}

/// Versão simplificada do botão de logout apenas com ícone
class LogoutIconButton extends StatelessWidget {
  const LogoutIconButton({super.key});

  @override
  Widget build(BuildContext context) {
    return const LogoutButton(
      showText: false,
      icon: Icons.logout,
    );
  }
}

/// Versão do botão de logout como item de menu
class LogoutMenuItem extends StatelessWidget {
  const LogoutMenuItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<FirebaseAuthService>(
      builder: (context, authService, _) {
        return ListTile(
          leading: const Icon(Icons.logout, color: AppTheme.errorRed),
          title: const Text('Sair'),
          onTap: () async {
            await _showLogoutDialog(context, authService);
          },
        );
      },
    );
  }

  Future<void> _showLogoutDialog(
    BuildContext context,
    FirebaseAuthService authService,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmar Logout'),
          content: const Text('Tem certeza que deseja sair da aplicação?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorRed,
              ),
              child: const Text(
                'Sair',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );

    if (result == true) {
      try {
        await authService.signOut();
        if (context.mounted) {
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/login',
            (route) => false,
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erro ao fazer logout: $e'),
              backgroundColor: AppTheme.errorRed,
            ),
          );
        }
      }
    }
  }
}
