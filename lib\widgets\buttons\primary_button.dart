import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';

enum ButtonType { primary, secondary, error }

class CustomButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String label;
  final Widget? icon;
  final ButtonType type;

  const CustomButton({
    super.key,
    this.onPressed,
    required this.label,
    this.icon,
    this.type = ButtonType.primary,
  });

  Color get textColor => switch (type) {
        ButtonType.primary => Colors.white,
        ButtonType.secondary => foreground,
        ButtonType.error => Colors.white,
      };

  ButtonStyle get buttonStyle => switch (type) {
        ButtonType.primary => ElevatedButton.styleFrom(
            backgroundColor: primaryGreen,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ButtonType.secondary => OutlinedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: foreground,
            side: const BorderSide(color: background, width: 2),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ButtonType.error => ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
      };

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: icon,
      label: Text(
        label,
        style: textMd.copyWith(color: textColor),
      ),
      style: buttonStyle,
    );
  }
}
