import 'package:flutter/material.dart';

import '../utils/app_theme.dart';

class RankingsContent extends StatefulWidget {
  const RankingsContent({super.key});

  @override
  State<RankingsContent> createState() => _RankingsContentState();
}

class _RankingsContentState extends State<RankingsContent> {
  String _selectedPeriod = 'Profissões';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header com filtros
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  _buildPeriodChip('Profissões'),
                  const SizedBox(width: 12),
                  _buildPeriodChip('Último mês'),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Implementar exportação
                },
                icon: const Icon(Icons.download),
                label: const Text('Exportar'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorRed,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profissões mais procuradas
              Expanded(
                flex: 2,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.emoji_events,
                                color: AppTheme.primaryGreen),
                            const SizedBox(width: 8),
                            Text(
                              'Profissões Mais Procuradas',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Ranking baseado no número de buscas e atendimentos agendados',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: AppTheme.textGray.withValues(alpha: 0.7),
                              ),
                        ),
                        const SizedBox(height: 24),

                        // Lista de profissões
                        Column(
                          children: [
                            _buildProfessionRankItem(
                              position: 1,
                              icon: Icons.local_hospital,
                              title: 'Enfermeiro',
                              searches: '1,847 buscas',
                              appointments: '324 atendimentos',
                              change: '+15%',
                              changeColor: AppTheme.primaryGreen,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionRankItem(
                              position: 2,
                              icon: Icons.accessibility,
                              title: 'Fisioterapeuta',
                              searches: '1,523 buscas',
                              appointments: '287 atendimentos',
                              change: '+8%',
                              changeColor: AppTheme.primaryGreen,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionRankItem(
                              position: 3,
                              icon: Icons.medical_services,
                              title: 'Médico Domiciliar',
                              searches: '1,341 buscas',
                              appointments: '256 atendimentos',
                              change: '+22%',
                              changeColor: AppTheme.primaryGreen,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionRankItem(
                              position: 4,
                              icon: Icons.psychology,
                              title: 'Psicólogo',
                              searches: '987 buscas',
                              appointments: '189 atendimentos',
                              change: '+12%',
                              changeColor: AppTheme.primaryGreen,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionRankItem(
                              position: 5,
                              icon: Icons.restaurant,
                              title: 'Nutricionista',
                              searches: '856 buscas',
                              appointments: '145 atendimentos',
                              change: '+5%',
                              changeColor: AppTheme.primaryGreen,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 24),

              // Profissionais que mais atenderam
              Expanded(
                flex: 1,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Profissionais que Mais Atenderam',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Ranking dos profissionais com maior número de atendimentos',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: AppTheme.textGray.withValues(alpha: 0.7),
                              ),
                        ),
                        const SizedBox(height: 24),

                        // Lista de profissionais
                        Column(
                          children: [
                            _buildProfessionalRankItem(
                              position: 1,
                              initials: 'DMS',
                              name: 'Dr. Maria Silva',
                              profession: 'Enfermeira',
                              appointments: '89 atendimentos',
                              rating: '4.9',
                              avatarColor: AppTheme.primaryGreen,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionalRankItem(
                              position: 2,
                              initials: 'CS',
                              name: 'Carlos Santos',
                              profession: 'Fisioterapeuta',
                              appointments: '76 atendimentos',
                              rating: '4.8',
                              avatarColor: AppTheme.primaryBlue,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionalRankItem(
                              position: 3,
                              initials: 'AC',
                              name: 'Ana Costa',
                              profession: 'Psicóloga',
                              appointments: '68 atendimentos',
                              rating: '4.9',
                              avatarColor: AppTheme.primaryPurple,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionalRankItem(
                              position: 4,
                              initials: 'JO',
                              name: 'João Oliveira',
                              profession: 'Médico',
                              appointments: '64 atendimentos',
                              rating: '4.7',
                              avatarColor: Colors.orange,
                            ),
                            const SizedBox(height: 16),
                            _buildProfessionalRankItem(
                              position: 5,
                              initials: 'FL',
                              name: 'Fernanda Lima',
                              profession: 'Nutricionista',
                              appointments: '58 atendimentos',
                              rating: '4.8',
                              avatarColor: Colors.pink,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Gráfico de buscas vs atendimentos
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Buscas vs Atendimentos',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Comparativo entre buscas realizadas e atendimentos efetivos',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                  ),
                  const SizedBox(height: 24),

                  // Placeholder para gráfico
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundGray,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppTheme.borderGray),
                    ),
                    child: const Center(
                      child: Text(
                        'Gráfico de Barras\n(Buscas vs Atendimentos)',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: AppTheme.textGray,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Métricas de visibilidade
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Métricas de Visibilidade dos Profissionais',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Análise detalhada de aparições em listagens e cliques nos perfis',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                  ),
                  const SizedBox(height: 24),

                  // Tabela de métricas
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.borderGray),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        // Header da tabela
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: const BoxDecoration(
                            color: AppTheme.backgroundGray,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: const Row(
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Text('Posição',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 3,
                                  child: Text('Profissional',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 2,
                                  child: Text('Profissão',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 2,
                                  child: Text('Aparições em Listagens',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 2,
                                  child: Text('Cliques no Perfil',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 1,
                                  child: Text('Taxa de Clique',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                              Expanded(
                                  flex: 1,
                                  child: Text('Taxa de Conversão',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600))),
                            ],
                          ),
                        ),

                        // Linhas da tabela
                        _buildVisibilityRow(1, 'DMS', 'Dr. Maria Silva',
                            'Enfermeira', '2,341', '1,234', '52.7%', '7.2%'),
                        _buildVisibilityRow(2, 'CS', 'Carlos Santos',
                            'Fisioterapeuta', '1,876', '987', '52.6%', '7.7%'),
                        _buildVisibilityRow(3, 'AC', 'Ana Costa', 'Psicóloga',
                            '1,654', '843', '51.0%', '8.1%'),
                        _buildVisibilityRow(4, 'JO', 'João Oliveira', 'Médico',
                            '1,523', '756', '49.6%', '8.5%'),
                        _buildVisibilityRow(5, 'FL', 'Fernanda Lima',
                            'Nutricionista', '1,298', '689', '53.1%', '8.4%'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodChip(String label) {
    final isSelected = _selectedPeriod == label;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedPeriod = label;
        });
      },
      selectedColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryBlue,
    );
  }

  Widget _buildProfessionRankItem({
    required int position,
    required IconData icon,
    required String title,
    required String searches,
    required String appointments,
    required String change,
    required Color changeColor,
  }) {
    return Row(
      children: [
        // Posição
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: position <= 3 ? AppTheme.primaryGreen : AppTheme.textGray,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Center(
            child: Text(
              position.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        const SizedBox(width: 16),

        // Ícone
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.primaryBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: AppTheme.primaryBlue,
          ),
        ),

        const SizedBox(width: 16),

        // Conteúdo
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.search, size: 14, color: AppTheme.textGray),
                  const SizedBox(width: 4),
                  Text(
                    searches,
                    style:
                        const TextStyle(fontSize: 12, color: AppTheme.textGray),
                  ),
                  const SizedBox(width: 16),
                  const Icon(Icons.calendar_today,
                      size: 14, color: AppTheme.textGray),
                  const SizedBox(width: 4),
                  Text(
                    appointments,
                    style:
                        const TextStyle(fontSize: 12, color: AppTheme.textGray),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Mudança
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: changeColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            change,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: changeColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfessionalRankItem({
    required int position,
    required String initials,
    required String name,
    required String profession,
    required String appointments,
    required String rating,
    required Color avatarColor,
  }) {
    return Row(
      children: [
        // Posição
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: position <= 3 ? AppTheme.primaryGreen : AppTheme.textGray,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              position.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),

        const SizedBox(width: 12),

        // Avatar
        CircleAvatar(
          radius: 16,
          backgroundColor: avatarColor,
          child: Text(
            initials,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ),

        const SizedBox(width: 12),

        // Conteúdo
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              Text(
                profession,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textGray,
                ),
              ),
              Text(
                appointments,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textGray,
                ),
              ),
              Row(
                children: [
                  const Icon(Icons.star, size: 12, color: Colors.amber),
                  const SizedBox(width: 4),
                  Text(
                    rating,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVisibilityRow(
    int position,
    String initials,
    String name,
    String profession,
    String appearances,
    String clicks,
    String clickRate,
    String conversionRate,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderGray),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Text(
              position.toString(),
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            flex: 3,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 12,
                  backgroundColor: AppTheme.primaryGreen,
                  child: Text(
                    initials,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(name),
              ],
            ),
          ),
          Expanded(flex: 2, child: Text(profession)),
          Expanded(flex: 2, child: Text(appearances)),
          Expanded(flex: 2, child: Text(clicks)),
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                clickRate,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                conversionRate,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryBlue,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
