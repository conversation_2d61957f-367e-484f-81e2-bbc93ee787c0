import 'package:get_it/get_it.dart';
import 'package:homecare_nexus_clone/screens/administrators_content/controller/cubits/administrators_content_cubit.dart';
import 'package:homecare_nexus_clone/screens/users_content/controller/cubits/users_content_cubit.dart';
import 'package:homecare_nexus_clone/screens/validation_content/controller/cubits/validation_content_cubit.dart';

final userContentCubit = GetIt.instance.get<UserContentCubit>();
final administratorsContentCubit =
    GetIt.instance.get<AdministratorsContentCubit>();
final validationContentCubit = GetIt.instance.get<ValidationContentCubit>();
