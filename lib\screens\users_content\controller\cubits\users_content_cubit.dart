import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../models/app_user.dart';
import '../../../../utils/enums.dart';
import '../users_content_service.dart';
import 'users_content_state.dart';

class UserContentCubit extends Cubit<UserContentState> {
  final UserContentService _service;

  UserContentCubit(this._service) : super(const UserContentState()) {
    loadUsers();
  }

  Future<void> loadUsers() async {
    try {
      final users = await _service.getUsers();
      emit(state.copyWith(all: users, filtered: users));
    } catch (e) {
      // For now ignore errors; could add an error field to state later.
      emit(state.copyWith(all: [], filtered: []));
    }
  }

  void applyFilter({String? filter, String? search}) {
    final f = filter ?? state.filter;
    final s = (search ?? state.search).toLowerCase();

    List<AppUser> byFilter;
    if (f == 'Todos' || f.isEmpty) {
      byFilter = state.all;
    } else {
      // simple example: filter by role/label contained in user.type or similar
      byFilter = state.all
          .where((u) => u.type.toLowerCase() == f.toLowerCase())
          .toList();
    }

    final bySearch = s.isEmpty
        ? byFilter
        : byFilter.where((u) {
            return u.name.toLowerCase().contains(s) ||
                u.email.toLowerCase().contains(s);
          }).toList();

    emit(state.copyWith(filter: f, search: s, filtered: bySearch));
  }

  Future<void> refresh() async => await loadUsers();

  /// Atualiza o status de um usuário localmente (mock)
  void setStatus(String userId, UserStatusEnum status) {
    final updatedAll = state.all.map((u) {
      if (u.id == userId) {
        return AppUser(
          id: u.id,
          name: u.name,
          email: u.email,
          phone: u.phone,
          type: u.type,
          specialty: u.specialty,
          location: u.location,
          status: status,
          appointments: u.appointments,
          lastAccess: u.lastAccess,
          documents: u.documents,
        );
      }
      return u;
    }).toList();

    // Re-apply current filter/search to the new list
    final previousFilter = state.filter;
    final previousSearch = state.search;
    emit(state.copyWith(all: updatedAll));
    applyFilter(filter: previousFilter, search: previousSearch);
  }
}
