import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/widgets/app_logo_widget.dart';

import '../../../utils/app_theme.dart';
import '../controller/cubits/login_cubit.dart';
import 'widgets/login_form.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  void _onLoginSuccess(BuildContext context) {
    Navigator.of(context).pushReplacementNamed('/dashboard');
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: Scaffold(
        backgroundColor: AppTheme.backgroundGray,
        body: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Center(
              child: Container(
                width: double.infinity,
                constraints:
                    const BoxConstraints(maxWidth: 450, minHeight: 600),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          AppLogoWidget(size: AppLogoSize.large),
                          SizedBox(height: 32),
                          Text('Acesso Administrativo',
                              style: textXl, textAlign: TextAlign.center),
                          SizedBox(height: 8),
                          Text(
                            'Faça login para gerenciar a plataforma',
                            style: textMd,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                      Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(32),
                          child: LoginForm(
                            onLoginSuccess: () => _onLoginSuccess(context),
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '© 2024 Achei Saúde. Todos os direitos reservados.',
                        style: textSm.copyWith(fontWeight: FontWeight.w400),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
