// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCyJL72JiKPpYnOe_Gw9m_m6G7gGWLbvsk',
    appId: '1:46860316363:web:1cafd380b5e2246f1d354e',
    messagingSenderId: '46860316363',
    projectId: 'acheisaude',
    authDomain: 'acheisaude.firebaseapp.com',
    storageBucket: 'acheisaude.appspot.com',
    measurementId: 'G-NLL7VQQX1H',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDgwOqEIXiDlLC6Z7L5rIbWpXraIxsVxR0',
    appId: '1:46860316363:ios:e7df0347ff2b2bc41d354e',
    messagingSenderId: '46860316363',
    projectId: 'acheisaude',
    storageBucket: 'acheisaude.appspot.com',
    iosBundleId: 'app.acheisaude.acheisaude',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBw58KGopBrN2RE7TIhwlZXfEwtM3nNWXY',
    appId: '1:46860316363:android:4c22a794f286d5741d354e',
    messagingSenderId: '46860316363',
    projectId: 'acheisaude',
    storageBucket: 'acheisaude.appspot.com',
  );

}