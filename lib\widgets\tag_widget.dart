import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';

enum TagType { border, primary, secondary, error }

class TagWidget extends StatelessWidget {
  final String title;
  final TagType type;
  final Widget? icon;

  const TagWidget({
    super.key,
    required this.title,
    this.type = TagType.border,
    this.icon,
  });

  Color get border => switch (type) {
        TagType.border => borderColor,
        TagType.primary => primaryGreen,
        TagType.secondary => background,
        TagType.error => errorColor,
      };

  Color get textColor => switch (type) {
        TagType.border => foreground,
        TagType.primary => Colors.white,
        TagType.secondary => foreground,
        TagType.error => Colors.white,
      };

  Color get backgroundColor => switch (type) {
        TagType.border => Colors.white,
        TagType.primary => primaryGreen,
        TagType.secondary => background,
        TagType.error => errorColor,
      };

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
          border: Border.all(color: border),
          borderRadius: BorderRadius.circular(12),
          color: backgroundColor),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment:
            icon == null ? MainAxisAlignment.center : MainAxisAlignment.start,
        children: [
          if (icon != null) ...[
            IconTheme.merge(
              data: IconThemeData(color: textColor, size: 12),
              child: icon!,
            ),
            const SizedBox(width: 4),
          ],
          Text(
            title,
            style: textXs.copyWith(
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
