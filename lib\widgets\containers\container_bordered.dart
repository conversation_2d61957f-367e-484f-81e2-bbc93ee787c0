import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';

class ContainerBordered extends Container {
  ContainerBordered({
    super.key,
    super.child,
    super.padding = const EdgeInsets.all(24),
  }) : super(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: borderColor),
            color: Colors.white,
          ),
        );
}
