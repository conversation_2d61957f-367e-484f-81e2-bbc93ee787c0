import 'package:homecare_nexus_clone/models/app_user.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';

class Admin extends AppUser {
  final String username;
  final AdmEnum role;
  final DateTime createdAt;

  Admin({
    required super.id,
    required super.name,
    required super.email,
    required super.phone,
    required super.type,
    super.specialty,
    required super.location,
    required super.status,
    required super.appointments,
    required super.lastAccess,
    List? documents,
    required this.username,
    required this.role,
    required this.createdAt,
  }) : super(
          documents: documents is List ? List.from(documents) : const [],
        );

  factory Admin.fromJson(Map<String, dynamic> json) {
    return Admin(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      type: json['type'] ?? 'Admin',
      specialty: json['specialty'],
      location: json['location'] ?? '',
      status: UserStatusEnum.values.firstWhere(
        (e) => e.toString().split('.').last == (json['status'] ?? 'active'),
        orElse: () => UserStatusEnum.active,
      ),
      appointments: json['appointments'] ?? 0,
      lastAccess: DateTime.tryParse(json['lastAccess'] ?? '') ?? DateTime.now(),
      documents: json['documents'] ?? [],
      username: json['username'] ?? '',
      role: AdmEnum.values.firstWhere(
        (e) => e.toString().split('.').last == (json['role'] ?? 'admin'),
        orElse: () => AdmEnum.admin,
      ),
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'username': username,
      'email': email,
      'phone': phone,
      'type': type,
      'specialty': specialty,
      'location': location,
      'status': status.toString().split('.').last,
      'appointments': appointments,
      'lastAccess': lastAccess.toIso8601String(),
      'documents': documents,
      'role': role.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
