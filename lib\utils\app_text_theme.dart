import 'package:flutter/widgets.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';

/// font size: 30
///
/// font weight: 700
///
/// line height: 2.25
///
/// color: foreground (#024b32)
const text3xl = TextStyle(
  fontSize: 30,
  height: 1.5,
  fontWeight: FontWeight.w700,
  color: foreground,
);

/// font size: 24
///
/// font weight: 700
///
/// line height: 2
///
/// color: foreground (#024b32)
const text2xl = TextStyle(
  fontSize: 24,
  height: 2,
  fontWeight: FontWeight.w700,
  color: foreground,
);

/// font size: 20
///
/// font weight: 600
///
/// line height: 1.5
///
/// color: foreground (#024b32)
const textXl = TextStyle(
  fontSize: 20,
  height: 1.5,
  fontWeight: FontWeight.w600,
  color: foreground,
);

/// font size: 18
///
/// font weight: 600
///
/// line height: 1.25
///
/// color: foreground (#024b32)
const textLg = TextStyle(
  fontSize: 18,
  height: 1.25,
  fontWeight: FontWeight.w600,
  color: foreground,
);

/// font size: 16
///
/// font weight: 500
///
/// line height: 1.25
///
/// color: mutedForeground (#6c9386)
const textMd = TextStyle(
  fontSize: 16,
  height: 1.25,
  fontWeight: FontWeight.w500,
  color: mutedForeground,
);

/// font size: 14
///
/// font weight: 500
///
/// line height: 1.25
///
/// color: mutedForeground (#6c9386)
const textSm = TextStyle(
  fontSize: 14,
  height: 1.25,
  fontWeight: FontWeight.w500,
  color: mutedForeground,
);

/// font size: 12
///
/// font weight: 400
///
/// line height: 1
///
/// color: mutedForeground (#6c9386)
const textXs = TextStyle(
  fontSize: 12,
  height: 1,
  fontWeight: FontWeight.w400,
  color: mutedForeground,
);
