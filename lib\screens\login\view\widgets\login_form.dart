import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';
import 'package:homecare_nexus_clone/widgets/textfields/email_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/name_text_field.dart';
import 'package:homecare_nexus_clone/widgets/textfields/password_text_field.dart';

import '../../controller/cubits/login_cubit.dart';
import '../../controller/cubits/login_state.dart';

class LoginForm extends StatefulWidget {
  final VoidCallback? onLoginSuccess;

  const LoginForm({super.key, this.onLoginSuccess});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() {
    if (!_formKey.currentState!.validate()) return;

    context.read<LoginCubit>().loginWithUsername(
          username: _usernameController.text,
          password: _passwordController.text,
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginCubit, LoginState>(
      listener: (context, state) {
        if (state.isSuccess) {
          widget.onLoginSuccess?.call();
        } else if (state.isFailure && state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: AppTheme.errorRed,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      child: BlocBuilder<LoginCubit, LoginState>(
        builder: (context, state) {
          return Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Entre com sua conta', style: textLg),
                const SizedBox(height: 8),
                const Text(
                  'Digite suas credenciais para acessar o painel',
                  style: textSm,
                ),
                const SizedBox(height: 24),
                NameTextField(
                  controller: _usernameController,
                  hintText: 'Digite seu usuário',
                  label: 'Usuário Admin',
                ),
                const SizedBox(height: 16),
                PasswordTextfield(
                  controller: _passwordController,
                  label: 'Senha Admin',
                  hintText: 'Digite sua senha',
                  onSubmit: (_) => _handleLogin(),
                ),
                const SizedBox(height: 16),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: state.isForgotPasswordLoading
                        ? null
                        : () => _showForgotPasswordDialog(context),
                    child: const Text(
                      'Esqueceu sua senha?',
                      style: TextStyle(
                        color: AppTheme.primaryGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: state.isLoading ? null : _handleLogin,
                    child: state.isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text('Entrar no Painel'),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showForgotPasswordDialog(BuildContext context) {
    final emailController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<LoginCubit>(),
        child: AlertDialog(
          title: const Text('Redefinir Senha'),
          content: BlocListener<LoginCubit, LoginState>(
            listener: (context, state) {
              if (state.isForgotPasswordSuccess) {
                Navigator.of(dialogContext).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.successMessage!),
                    backgroundColor: AppTheme.primaryGreen,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              } else if (state.isForgotPasswordFailure &&
                  state.errorMessage != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage!),
                    backgroundColor: AppTheme.errorRed,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Digite seu email para receber as instruções de redefinição de senha.',
                    style: textSm,
                  ),
                  const SizedBox(height: 16),
                  EmailTextField(
                    controller: emailController,
                    hintText: 'Digite seu email',
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancelar'),
            ),
            BlocBuilder<LoginCubit, LoginState>(
              builder: (context, state) {
                return ElevatedButton(
                  onPressed: state.isForgotPasswordLoading
                      ? null
                      : () {
                          if (formKey.currentState!.validate()) {
                            context.read<LoginCubit>().sendPasswordResetEmail(
                                  emailController.text,
                                );
                          }
                        },
                  child: state.isForgotPasswordLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Enviar'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
