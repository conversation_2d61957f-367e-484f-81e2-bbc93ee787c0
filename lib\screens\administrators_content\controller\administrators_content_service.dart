import '../../../models/admin.dart';
import '../../../utils/enums.dart';
import 'administrators_content_repository.dart';
import 'firebase_admin_service.dart';

class AdministratorsContentService {
  final AdministratorsContentRepository _repository;
  final FirebaseAdminService _firebaseService;

  AdministratorsContentService(
    this._repository, {
    FirebaseAdminService? firebaseService,
  }) : _firebaseService = firebaseService ?? FirebaseAdminService();

  /// Busca administradores do Firebase
  Future<List<Admin>> fetchAdmins() async {
    try {
      return await _firebaseService.getAdministrators();
    } catch (e) {
      // Fallback para dados locais em caso de erro
      return _repository.getAdmins();
    }
  }

  /// Cria um novo administrador no Firebase
  Future<AdminCreationResult> createAdmin({
    required String name,
    required String username,
    required String email,
    required String password,
    required AdmEnum role,
    String? phone,
    String location = 'Sede',
  }) async {
    // Validações básicas
    if (name.trim().isEmpty) {
      return AdminCreationResult.failure('Nome é obrigatório');
    }
    if (username.trim().isEmpty) {
      return AdminCreationResult.failure('Nome de usuário é obrigatório');
    }
    if (email.trim().isEmpty) {
      return AdminCreationResult.failure('Email é obrigatório');
    }
    if (password.trim().isEmpty) {
      return AdminCreationResult.failure('Senha é obrigatória');
    }
    if (password.length < 6) {
      return AdminCreationResult.failure(
          'Senha deve ter pelo menos 6 caracteres');
    }

    // Verifica se email já está em uso
    final emailInUse = await _firebaseService.isEmailInUse(email.trim());
    if (emailInUse) {
      return AdminCreationResult.failure('Este email já está sendo usado');
    }

    // Verifica se username já está em uso
    final usernameInUse =
        await _firebaseService.isUsernameInUse(username.trim());
    if (usernameInUse) {
      return AdminCreationResult.failure(
          'Este nome de usuário já está sendo usado');
    }

    return await _firebaseService.createAdmin(
      name: name.trim(),
      username: username.trim(),
      email: email.trim(),
      password: password,
      role: role,
      phone: phone?.trim(),
      location: location,
    );
  }

  /// Atualiza um administrador
  Future<void> updateAdmin(Admin admin) async {
    await _firebaseService.updateAdmin(admin);
  }

  /// Atualiza o status de um administrador
  Future<void> updateAdminStatus(String adminId, UserStatusEnum status) async {
    await _firebaseService.updateAdminStatus(adminId, status);
  }

  /// Deleta um administrador (soft delete)
  Future<void> deleteAdmin(String adminId) async {
    await _firebaseService.deleteAdmin(adminId);
  }

  /// Busca um administrador por ID
  Future<Admin?> getAdminById(String adminId) async {
    return await _firebaseService.getAdminById(adminId);
  }
}
