import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/core/injection.dart';
import 'package:provider/provider.dart';

import 'screens/base_inner_screen.dart';
import 'screens/login/view/login_screen.dart';
import 'services/auth_service.dart';
import 'services/navigation_service.dart';
import 'utils/app_theme.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  setupDependencies();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthService()),
        ChangeNotifierProvider(create: (_) => NavigationService()),
      ],
      child: MaterialApp(
        title: 'A<PERSON>i <PERSON> - <PERSON>min <PERSON>',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        home: Consumer<AuthService>(
          builder: (context, authService, _) {
            return authService.isAuthenticated
                ? const BaseInnerScreen()
                : const LoginScreen();
          },
        ),
        routes: {
          '/login': (context) => const LoginScreen(),
          '/dashboard': (context) => const BaseInnerScreen(),
        },
      ),
    );
  }
}
