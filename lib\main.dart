import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/core/injection.dart';
import 'package:provider/provider.dart';

import 'firebase_options.dart';
import 'screens/base_inner_screen.dart';
import 'screens/login/view/login_screen.dart';
import 'services/firebase_auth_service.dart';
import 'services/navigation_service.dart';
import 'utils/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  setupDependencies();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => FirebaseAuthService()),
        ChangeNotifierProvider(create: (_) => NavigationService()),
      ],
      child: MaterialApp(
        title: '<PERSON><PERSON><PERSON> - <PERSON>min <PERSON>',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        home: Consumer<FirebaseAuthService>(
          builder: (context, authService, _) {
            // Mostra loading enquanto inicializa
            if (!authService.isInitialized) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            return authService.isAuthenticated
                ? const BaseInnerScreen()
                : const LoginScreen();
          },
        ),
        routes: {
          '/login': (context) => const LoginScreen(),
          '/dashboard': (context) => const BaseInnerScreen(),
        },
      ),
    );
  }
}
