import 'package:flutter/material.dart';

class BodyCell extends StatelessWidget {
  final Widget child;
  final double? width;
  final EdgeInsets? padding;

  const BodyCell({
    super.key,
    required this.child,
    this.width,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        height: 64,
        alignment: Alignment.centerLeft,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFE5E7EB),
              width: 1,
            ),
          ),
        ),
        child: child,
      ),
    );
  }
}
