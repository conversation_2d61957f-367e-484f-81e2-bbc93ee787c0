import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/core/global_instances.dart';
import 'package:homecare_nexus_clone/models/app_user.dart';
import 'package:homecare_nexus_clone/screens/user_details/view/user_details_screen.dart';
import 'package:homecare_nexus_clone/utils/app_colors.dart';
import 'package:homecare_nexus_clone/utils/app_text_theme.dart';
import 'package:homecare_nexus_clone/utils/app_theme.dart';
import 'package:homecare_nexus_clone/utils/date_formatter.dart';
import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/utils/extensions.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

class UserContentRow extends StatelessWidget {
  final AppUser userModel;

  const UserContentRow({super.key, required this.userModel});

  @override
  Widget build(BuildContext context) {
    final displayName = userModel.name;
    final displayEmail = userModel.email;
    final displayPhone = userModel.phone;
    final displayType = userModel.type;
    final displaySpecialty = userModel.specialty ?? '-';
    final displayLocation = userModel.location;
    final displayStatus = userModel.status;
    final displayAppointments = userModel.appointments.toString();
    final displayLastAccess = DateFormatter.formatDate(userModel.lastAccess);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderGray),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  displayName,
                  style: textSm.copyWith(color: foreground),
                ),
                Text(
                  displayEmail,
                  style: textSm.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppTheme.textGray.withValues(alpha: 0.7),
                  ),
                ),
                Text(
                  displayPhone,
                  style: textSm.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppTheme.textGray.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerLeft,
              child: TagWidget(title: displayType),
            ),
          ),
          Expanded(flex: 2, child: Text(displaySpecialty)),
          Expanded(
            flex: 2,
            child: Row(
              children: [
                const Icon(Icons.location_on_outlined, size: 14),
                Text(displayLocation),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerLeft,
              child: TagWidget(
                title: displayStatus.displayName,
                type: displayStatus.tagType,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(displayAppointments),
          ),
          Expanded(
            flex: 1,
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 14),
                const SizedBox(width: 4),
                Text(
                  displayLastAccess,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerLeft,
              child: PopupMenuButton<String>(
                padding: EdgeInsets.zero,
                icon: const Icon(Icons.more_vert, size: 18),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                onSelected: (value) {
                  if (value == 'details') {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => UserDetailsScreen(user: userModel),
                      ),
                    );
                  } else if (value == 'disable') {
                    userContentCubit.setStatus(
                        userModel.id, UserStatusEnum.inactive);
                  } else if (value == 'suspend') {
                    userContentCubit.setStatus(
                        userModel.id, UserStatusEnum.suspended);
                  } else if (value == 'activate') {
                    userContentCubit.setStatus(
                        userModel.id, UserStatusEnum.active);
                  }
                },
                itemBuilder: (context) {
                  final List<PopupMenuEntry<String>> items = [];
                  items.add(
                    PopupMenuItem<String>(
                      enabled: false,
                      child: Text(
                        'Ações',
                        style: textSm.copyWith(
                          fontWeight: FontWeight.w600,
                          color: foreground,
                        ),
                      ),
                    ),
                  );

                  items.add(
                    PopupMenuItem(
                      value: 'details',
                      child: Row(
                        children: [
                          const Icon(
                            Icons.visibility_outlined,
                            size: 16,
                            color: foreground,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Ver Detalhes',
                            style: textMd.copyWith(
                              color: foreground,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );

                  items.add(const PopupMenuDivider());

                  if (displayStatus != UserStatusEnum.active) {
                    items.add(
                      PopupMenuItem(
                        value: 'activate',
                        child: Row(
                          children: [
                            const Icon(
                              Icons.check_circle_outline,
                              size: 16,
                              color: foreground,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Ativar',
                              style: textMd.copyWith(
                                color: foreground,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  if (displayStatus != UserStatusEnum.inactive) {
                    items.add(PopupMenuItem(
                      value: 'disable',
                      child: Row(
                        children: [
                          const Icon(
                            Icons.person_remove_outlined,
                            size: 16,
                            color: foreground,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Desabilitar',
                            style: textMd.copyWith(
                              color: foreground,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ));
                  }
                  if (displayStatus != UserStatusEnum.suspended) {
                    items.add(
                      PopupMenuItem(
                        value: 'suspend',
                        child: Row(
                          children: [
                            const Icon(
                              Icons.person_off_outlined,
                              size: 16,
                              color: foreground,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Suspender',
                              style: textMd.copyWith(
                                color: foreground,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return items;
                },
              ),
            ),
          )
        ],
      ),
    );
  }
}
