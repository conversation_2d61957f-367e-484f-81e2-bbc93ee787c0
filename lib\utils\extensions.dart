import 'package:homecare_nexus_clone/utils/enums.dart';
import 'package:homecare_nexus_clone/widgets/tag_widget.dart';

extension AdmRoleExtension on AdmEnum {
  String get displayName {
    switch (this) {
      case AdmEnum.superAdmin:
        return 'Super Admin';
      case AdmEnum.admin:
        return 'Administrador';
      case AdmEnum.moderator:
        return 'Moderador';
    }
  }
}

extension UserStatusExtension on UserStatusEnum {
  String get displayName {
    switch (this) {
      case UserStatusEnum.active:
        return 'Ativo';
      case UserStatusEnum.inactive:
        return 'Inativo';
      case UserStatusEnum.suspended:
        return 'Suspenso';
      case UserStatusEnum.pending:
        return 'Pendente';
      case UserStatusEnum.inReview:
        return 'Em Análise';
      case UserStatusEnum.rejected:
        return 'Rejeitado';
      case UserStatusEnum.pendingUpdate:
        return 'Atualização Pendente';
    }
  }

  TagType get tagType {
    switch (this) {
      case UserStatusEnum.active:
        return TagType.primary;
      case UserStatusEnum.inactive:
        return TagType.secondary;
      case UserStatusEnum.suspended:
        return TagType.error;
      case UserStatusEnum.pending:
        return TagType.secondary;
      case UserStatusEnum.inReview:
        return TagType.border;
      case UserStatusEnum.rejected:
        return TagType.error;
      case UserStatusEnum.pendingUpdate:
        return TagType.secondary;
    }
  }
}

extension DocumentStatusExtension on DocumentStatus? {
  String get displayName {
    switch (this) {
      case DocumentStatus.approved:
        return 'Aprovado';
      case DocumentStatus.rejected:
        return 'Rejeitado';
      case DocumentStatus.pending:
        return 'Pendente';
      default:
        return 'Pendente';
    }
  }

  TagType get tagType {
    switch (this) {
      case DocumentStatus.approved:
        return TagType.primary;
      case DocumentStatus.rejected:
        return TagType.error;
      case DocumentStatus.pending:
      default:
        return TagType.secondary;
    }
  }
}

extension DocumentStatusNonNullableExtension on DocumentStatus {
  String get displayName {
    switch (this) {
      case DocumentStatus.approved:
        return 'Aprovado';
      case DocumentStatus.rejected:
        return 'Rejeitado';
      case DocumentStatus.pending:
        return 'Pendente';
    }
  }

  TagType get tagType {
    switch (this) {
      case DocumentStatus.approved:
        return TagType.primary;
      case DocumentStatus.rejected:
        return TagType.error;
      case DocumentStatus.pending:
        return TagType.secondary;
    }
  }

  TagWidget tag() {
    return TagWidget(
      title: displayName,
      type: tagType,
    );
  }
}
