import 'package:flutter/material.dart';

import '../utils/app_theme.dart';
import 'dashboard_content.dart';

class ActivityCard extends StatelessWidget {
  final String description;
  final String timestamp;
  final ActivityStatus status;

  const ActivityCard({
    super.key,
    required this.description,
    required this.timestamp,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundGray,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.borderGray),
      ),
      child: Row(
        children: [
          // Status indicator
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getStatusColor(),
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  timestamp,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textGray.withValues(alpha: 0.7),
                      ),
                ),
              ],
            ),
          ),

          // Status badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getStatusText(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: _getStatusColor(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case ActivityStatus.approved:
        return AppTheme.primaryGreen;
      case ActivityStatus.rejected:
        return AppTheme.errorRed;
      case ActivityStatus.pending:
        return Colors.orange;
      case ActivityStatus.completed:
        return AppTheme.primaryGreen;
      case ActivityStatus.new_item:
        return AppTheme.primaryBlue;
    }
  }

  String _getStatusText() {
    switch (status) {
      case ActivityStatus.approved:
        return 'Aprovado';
      case ActivityStatus.rejected:
        return 'Rejeitado';
      case ActivityStatus.pending:
        return 'Pendente';
      case ActivityStatus.completed:
        return 'Concluído';
      case ActivityStatus.new_item:
        return 'Novo';
    }
  }
}
