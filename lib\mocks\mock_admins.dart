import '../models/admin.dart';
import '../utils/enums.dart';

final mockAdmins = [
  Admin(
    id: 'adm1',
    name: '<PERSON>',
    email: 'luca<PERSON>@example.com',
    phone: '111111111',
    type: 'Admin',
    specialty: null,
    location: 'Sede',
    status: UserStatusEnum.active,
    appointments: 0,
    lastAccess: DateTime.parse('2023-09-01'),
    documents: [],
    username: 'luca<PERSON>',
    role: AdmEnum.admin,
    createdAt: DateTime.parse('2023-01-10'),
  ),
  Admin(
    id: 'adm2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '222222222',
    type: 'Admin',
    specialty: null,
    location: 'Sede',
    status: UserStatusEnum.active,
    appointments: 0,
    lastAccess: DateTime.parse('2023-08-30'),
    documents: [],
    username: 'mariana',
    role: AdmEnum.superAdmin,
    createdAt: DateTime.parse('2022-11-20'),
  ),
  Admin(
    id: 'adm3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '333333333',
    type: 'Admin',
    specialty: null,
    location: 'Filial',
    status: UserStatusEnum.inactive,
    appointments: 0,
    lastAccess: DateTime.parse('2023-06-12'),
    documents: [],
    username: 'rodrigo',
    role: AdmEnum.moderator,
    createdAt: DateTime.parse('2023-04-05'),
  ),
];
