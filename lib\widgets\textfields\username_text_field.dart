import 'package:flutter/material.dart';
import 'package:homecare_nexus_clone/utils/validators.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

class UsernameTextField extends TextFieldCustom {
  const UsernameTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.isEnabled,
    super.controller,
    super.initialValue,
    super.textInputAction,
    super.hintText,
    super.onSubmit,
  }) : super(
          label: 'Usuário',
          keyboardType: TextInputType.text,
          validator: validateDefault,
        );
}
