import 'package:flutter/services.dart';
import 'package:homecare_nexus_clone/widgets/textfields/text_field_custom.dart';

class NumberTextField extends TextFieldCustom {
  NumberTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.hintText,
    super.textInputAction,
    super.initialValue,
    super.controller,
    super.validator,
    super.isEnabled,
    List<TextInputFormatter>? inputFormatters,
  }) : super(
          keyboardType: TextInputType.number,
          inputFormatters: [
            if (inputFormatters != null) ...inputFormatters,
            FilteringTextInputFormatter.digitsOnly
          ],
        );
}
