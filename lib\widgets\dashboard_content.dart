import 'package:flutter/material.dart';

import '../utils/app_theme.dart';
import '../widgets/activity_card.dart';
import '../widgets/notification_card.dart';
import '../widgets/stat_card.dart';

class DashboardContent extends StatelessWidget {
  const DashboardContent({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cards de estatísticas
          const Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Total de Usuários',
                  value: '2,847',
                  change: '+12%',
                  changeType: ChangeType.positive,
                  subtitle: 'vs. mês anterior',
                  description: 'Profissionais e pacientes ativos',
                  icon: Icons.people,
                  iconColor: AppTheme.primaryBlue,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Pendentes de Validação',
                  value: '23',
                  change: '-5%',
                  changeType: ChangeType.negative,
                  subtitle: 'vs. mês anterior',
                  description: 'Documentos aguardando aprovação',
                  icon: Icons.pending_actions,
                  iconColor: AppTheme.primaryPurple,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Atendimentos Hoje',
                  value: '156',
                  change: '+18%',
                  changeType: ChangeType.positive,
                  subtitle: 'vs. mês anterior',
                  description: 'Consultas agendadas para hoje',
                  icon: Icons.calendar_today,
                  iconColor: AppTheme.primaryGreen,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: StatCard(
                  title: 'Taxa de Aprovação',
                  value: '94.2%',
                  change: '+2.1%',
                  changeType: ChangeType.positive,
                  subtitle: 'vs. mês anterior',
                  description: 'Perfis aprovados este mês',
                  icon: Icons.check_circle,
                  iconColor: AppTheme.primaryGreen,
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Seção de atividades e notificações
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Atividades recentes
              Expanded(
                flex: 2,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Atividades Recentes',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Últimas ações realizadas na plataforma',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: AppTheme.textGray
                                            .withValues(alpha: 0.7),
                                      ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Lista de atividades
                        const Column(
                          children: [
                            ActivityCard(
                              description:
                                  'Documento de Dr. João Silva aprovado',
                              timestamp: '2 min atrás',
                              status: ActivityStatus.approved,
                            ),
                            SizedBox(height: 12),
                            ActivityCard(
                              description:
                                  'Novo paciente cadastrado: Maria Santos',
                              timestamp: '5 min atrás',
                              status: ActivityStatus.new_item,
                            ),
                            SizedBox(height: 12),
                            ActivityCard(
                              description:
                                  'Certificado de Dra. Ana Costa rejeitado',
                              timestamp: '12 min atrás',
                              status: ActivityStatus.rejected,
                            ),
                            SizedBox(height: 12),
                            ActivityCard(
                              description:
                                  'Atendimento concluído: Carlos Oliveira',
                              timestamp: '18 min atrás',
                              status: ActivityStatus.completed,
                            ),
                            SizedBox(height: 12),
                            ActivityCard(
                              description:
                                  'Perfil de Dr. Pedro Lima em análise',
                              timestamp: '25 min atrás',
                              status: ActivityStatus.pending,
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Botão ver todas
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: () {
                              // TODO: Implementar navegação para todas as atividades
                            },
                            icon: const Icon(Icons.visibility),
                            label: const Text('Ver Todas as Atividades'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: const BorderSide(color: AppTheme.errorRed),
                              foregroundColor: AppTheme.errorRed,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 24),

              // Alertas e notificações
              Expanded(
                flex: 1,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Alertas e Notificações',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Informações importantes que requerem atenção',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: AppTheme.textGray
                                        .withValues(alpha: 0.7),
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Lista de notificações
                        const Column(
                          children: [
                            NotificationCard(
                              title: 'Documentos Pendentes',
                              description:
                                  '23 documentos aguardando validação há mais de 24h',
                              type: NotificationType.urgent,
                              icon: Icons.warning,
                            ),
                            SizedBox(height: 16),
                            NotificationCard(
                              title: 'Backup Automático',
                              description:
                                  'Backup realizado com sucesso às 03:00',
                              type: NotificationType.info,
                              icon: Icons.backup,
                            ),
                            SizedBox(height: 16),
                            NotificationCard(
                              title: 'Meta Atingida',
                              description:
                                  'Meta de 95% de satisfação foi alcançada',
                              type: NotificationType.success,
                              icon: Icons.check_circle,
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Botão ver todas
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: () {
                              // TODO: Implementar navegação para todas as notificações
                            },
                            icon: const Icon(Icons.notifications),
                            label: const Text('Ver Todas as Notificações'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side:
                                  const BorderSide(color: AppTheme.primaryBlue),
                              foregroundColor: AppTheme.primaryBlue,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Ações rápidas
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Ações Rápidas',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Acesso direto às funcionalidades mais utilizadas',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray.withValues(alpha: 0.7),
                        ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // TODO: Navegar para validação
                          },
                          icon: const Icon(Icons.verified_user),
                          label: const Text('Validar Documentos'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // TODO: Navegar para usuários
                          },
                          icon: const Icon(Icons.people),
                          label: const Text('Gerenciar Usuários'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // TODO: Navegar para relatórios
                          },
                          icon: const Icon(Icons.analytics),
                          label: const Text('Ver Relatórios'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

enum ActivityStatus { approved, rejected, pending, completed, new_item }

enum NotificationType { urgent, info, success }
