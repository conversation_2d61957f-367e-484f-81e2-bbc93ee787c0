# Achei Saúde - Admin Panel Clone

Este é um clone completo em Flutter do painel administrativo do site Achei Saúde, replicando todas as funcionalidades de navegação e clique do site original.

## 📱 Sobre o Projeto

O projeto é uma recriação fiel do painel administrativo da plataforma Achei Saúde, desenvolvido em Flutter para demonstrar as capacidades de desenvolvimento mobile multiplataforma. O aplicativo inclui todas as telas principais e funcionalidades interativas do site original.

## 🎯 Funcionalidades Implementadas

### Autenticação
- Tela de login com validação
- Sistema de autenticação simulado
- Persistência de sessão
- Logout com confirmação

### Dashboard Principal
- Visão geral com estatísticas em tempo real
- Cards de métricas principais (usuários, validações, atendimentos)
- Lista de atividades recentes
- Alertas e notificações importantes
- Ações rápidas para funcionalidades principais

### Gestão de Usuários
- Lista completa de usuários (profissionais e pacientes)
- Filtros por tipo de usuário e status
- Busca por nome ou email
- Visualização de detalhes do usuário
- Edição de informações
- Ações de suspensão e ativação

### Administradores
- Lista de administradores do sistema
- Diferentes níveis de permissão (Super Admin, Admin, Moderador)
- Criação de novos administradores
- Edição de permissões e informações
- Controle de status (ativo/inativo)

### Validação de Usuários
- Lista de documentos pendentes de validação
- Filtros por tipo de validação
- Sistema de controle de concorrência
- Início de processo de validação
- Aprovação e rejeição de documentos

### Relatórios
- Estatísticas detalhadas da plataforma
- Gráficos de crescimento e tendências
- Métricas de usuários e atendimentos
- Exportação de dados
- Filtros por período

### Rankings
- Profissões mais procuradas
- Profissionais com melhor desempenho
- Métricas de visibilidade
- Análise de conversão
- Exportação de rankings

## 🏗️ Arquitetura do Projeto

### Estrutura de Pastas

```
lib/
├── main.dart                 # Ponto de entrada da aplicação
├── models/                   # Modelos de dados
│   ├── user.dart            # Modelo de usuário
│   ├── admin.dart           # Modelo de administrador
│   └── dashboard_data.dart  # Modelo de dados do dashboard
├── screens/                  # Telas principais
│   ├── login_screen.dart    # Tela de login
│   └── dashboard_screen.dart # Tela principal do dashboard
├── widgets/                  # Componentes reutilizáveis
│   ├── sidebar.dart         # Barra lateral de navegação
│   ├── dashboard_content.dart # Conteúdo do dashboard
│   ├── users_content.dart   # Conteúdo da gestão de usuários
│   ├── administrators_content.dart # Conteúdo dos administradores
│   ├── validation_content.dart # Conteúdo da validação
│   ├── reports_content.dart # Conteúdo dos relatórios
│   ├── rankings_content.dart # Conteúdo dos rankings
│   ├── stat_card.dart       # Card de estatística
│   ├── activity_card.dart   # Card de atividade
│   ├── notification_card.dart # Card de notificação
│   └── clickable_components.dart # Componentes clicáveis
├── services/                 # Serviços e lógica de negócio
│   ├── auth_service.dart    # Serviço de autenticação
│   ├── data_service.dart    # Serviço de dados
│   └── navigation_service.dart # Serviço de navegação
└── utils/                    # Utilitários
    └── app_theme.dart       # Tema e cores da aplicação
```

### Padrões Utilizados

#### Provider Pattern
O projeto utiliza o padrão Provider para gerenciamento de estado, com três principais providers:

- **AuthService**: Gerencia autenticação e sessão do usuário
- **DataService**: Gerencia dados da aplicação e operações CRUD
- **NavigationService**: Gerencia navegação entre telas e rotas

#### Component-Based Architecture
Cada tela é dividida em componentes menores e reutilizáveis, facilitando manutenção e testes.

#### Service Layer
Toda a lógica de negócio está encapsulada em serviços, separando responsabilidades e facilitando testes unitários.

## 🎨 Design System

### Paleta de Cores

```dart
// Cores principais
primaryGreen: Color(0xFF10B981)    // Verde principal
primaryBlue: Color(0xFF3B82F6)     // Azul principal  
primaryPurple: Color(0xFF8B5CF6)   // Roxo principal
errorRed: Color(0xFFEF4444)        // Vermelho de erro

// Cores de fundo
backgroundWhite: Color(0xFFFFFFFF)  // Fundo branco
backgroundGray: Color(0xFFF9FAFB)   // Fundo cinza claro

// Cores de texto
textGray: Color(0xFF374151)         // Texto principal
borderGray: Color(0xFFE5E7EB)       // Bordas
```

### Tipografia
- **Fonte**: Sistema padrão (Roboto no Android, San Francisco no iOS)
- **Tamanhos**: Escala responsiva baseada no Material Design
- **Pesos**: Regular (400), Medium (500), SemiBold (600), Bold (700)

### Componentes
- Cards com elevação sutil
- Botões com estados hover e pressed
- Inputs com validação visual
- Tabelas responsivas
- Modais e dialogs

## 🔧 Funcionalidades Técnicas

### Navegação
- Navegação por abas na sidebar
- Roteamento interno sem mudança de URL
- Breadcrumbs automáticos
- Histórico de navegação

### Interatividade
- Cliques em todos os elementos interativos
- Feedback visual para ações
- Loading states
- Confirmações para ações críticas

### Responsividade
- Layout adaptativo para diferentes tamanhos de tela
- Sidebar colapsável em telas menores
- Tabelas com scroll horizontal
- Cards que se reorganizam automaticamente

### Performance
- Lazy loading de conteúdo
- Otimização de renderização
- Cache de dados
- Debounce em buscas

## 📊 Dados Simulados

O aplicativo utiliza dados simulados para demonstração, incluindo:

- 4 usuários de exemplo (2 profissionais, 2 pacientes)
- 3 administradores com diferentes níveis
- Estatísticas realistas do dashboard
- Atividades recentes simuladas
- Notificações de exemplo

## 🚀 Como Executar

### Pré-requisitos
- Flutter SDK (versão 3.0 ou superior)
- Dart SDK
- Android Studio ou VS Code
- Emulador Android/iOS ou dispositivo físico

### Instalação

1. Clone o repositório:
```bash
git clone <url-do-repositorio>
cd homecare_nexus_clone
```

2. Instale as dependências:
```bash
flutter pub get
```

3. Execute o aplicativo:
```bash
flutter run
```

### Credenciais de Teste
- **Usuário**: admin
- **Senha**: admin123

## 📱 Compatibilidade

- **Android**: API 21+ (Android 5.0+)
- **iOS**: iOS 11.0+
- **Web**: Navegadores modernos
- **Desktop**: Windows, macOS, Linux (com Flutter Desktop)

## 🧪 Testes

### Testes Implementados
- Testes unitários para serviços
- Testes de widget para componentes
- Testes de integração para fluxos principais

### Executar Testes
```bash
flutter test
```

## 📦 Dependências Principais

```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1        # Gerenciamento de estado
  cupertino_icons: ^1.0.2 # Ícones iOS

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0   # Linting
```

## 🔮 Próximos Passos

### Funcionalidades Futuras
- Integração com API real
- Notificações push
- Modo offline
- Exportação de relatórios em PDF
- Gráficos interativos
- Sistema de permissões avançado

### Melhorias Técnicas
- Implementação de testes automatizados
- CI/CD pipeline
- Monitoramento de performance
- Logs estruturados
- Tratamento de erros avançado

## 👥 Contribuição

Este projeto foi desenvolvido como um clone demonstrativo. Para contribuições:

1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto é apenas para fins demonstrativos e educacionais.

## 📞 Suporte

Para dúvidas ou suporte, entre em contato através dos canais oficiais do projeto.

---

**Desenvolvido com ❤️ usando Flutter**

