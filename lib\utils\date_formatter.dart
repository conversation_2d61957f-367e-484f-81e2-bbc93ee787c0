import 'package:intl/intl.dart';

class DateFormatter {
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final DateFormat _dateTimeFormat = DateFormat('dd/MM/yyyy HH:mm');

  /// Formats a DateTime to dd/MM/yyyy format
  static String formatDate(DateTime date) {
    return _dateFormat.format(date);
  }

  /// Formats a DateTime to dd/MM/yyyy HH:mm format
  static String formatDateTime(DateTime date) {
    return _dateTimeFormat.format(date);
  }
}