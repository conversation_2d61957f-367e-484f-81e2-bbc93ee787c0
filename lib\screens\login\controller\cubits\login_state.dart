import 'package:firebase_auth/firebase_auth.dart';

/// Estados possíveis para o login
enum LoginStatus {
  initial,
  loading,
  success,
  failure,
  forgotPasswordLoading,
  forgotPasswordSuccess,
  forgotPasswordFailure,
}

class LoginState {
  final LoginStatus status;
  final User? user;
  final String? errorMessage;
  final String? successMessage;
  final bool isPasswordVisible;

  const LoginState({
    this.status = LoginStatus.initial,
    this.user,
    this.errorMessage,
    this.successMessage,
    this.isPasswordVisible = false,
  });

  LoginState copyWith({
    LoginStatus? status,
    User? user,
    String? errorMessage,
    String? successMessage,
    bool? isPasswordVisible,
  }) {
    return LoginState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
      successMessage: successMessage ?? this.successMessage,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
    );
  }

  /// Limpa mensagens de erro e sucesso
  LoginState clearMessages() {
    return copyWith(
      errorMessage: null,
      successMessage: null,
    );
  }

  /// Verifica se está carregando
  bool get isLoading => status == LoginStatus.loading;

  /// Verifica se está carregando o esqueci senha
  bool get isForgotPasswordLoading =>
      status == LoginStatus.forgotPasswordLoading;

  /// Verifica se o login foi bem-sucedido
  bool get isSuccess => status == LoginStatus.success;

  /// Verifica se houve erro no login
  bool get isFailure => status == LoginStatus.failure;

  /// Verifica se o esqueci senha foi bem-sucedido
  bool get isForgotPasswordSuccess =>
      status == LoginStatus.forgotPasswordSuccess;

  /// Verifica se houve erro no esqueci senha
  bool get isForgotPasswordFailure =>
      status == LoginStatus.forgotPasswordFailure;
}
